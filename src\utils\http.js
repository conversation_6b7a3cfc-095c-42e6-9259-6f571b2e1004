import store from '@/store/index'

let BASE_URL = ''
// #ifdef MP-WEIXIN
BASE_URL = process.env.VUE_APP_BASE_URL
// #endif
// #ifdef H5
BASE_URL = process.env.VUE_APP_H5_BASE_URL
// #endif
export const http = (options) => {
  const token = store.state.token
    ? `Bearer ${store.state.token}`
    : options.token
      ? options.token
      : ''
  return new Promise((resolve, reject) => {
    uni.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        Authorization: options.noToken ? '' : token,
        ...options.header,
      },
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.code === 401) {
            console.log(res, 'res-401')
            resolve(res.data)
            store.commit('set_props', {
              token: '',
              ajhToken: '',
            })

            const pages = getCurrentPages()
            // 获取到当前页面的路径和参数
            const page = pages[pages.length - 1].$page.fullPath //完整路由地址
            console.log('登陆之前的页面', page)
            let backPage = [
              'pages/union/college/signInSuccess',
              'pages/community/social/manage/sign',
              'pages/community/activity/detail',
              'pages/finance/yfk/client/myprepaid',
              'pages/finance/yfk/client/prodDetails',
              'pages/activity/campsite/campsiteDetails',
              'pages/activity/home/<USER>',
              'pages/activity/activityDetail/index',
              'pages/activity/morePlaza/index',
              'pages/activity/mine/MyHome',
              'pages/holidaysActivity/vote/index',
              'pages/holidaysActivity/signIn/index',
              'pages/holidaysActivity/luckyDraw/index',
              'pages/diningCard/yhhx/yhsmhx'
            ]
            // 跳转回之前页面
            if (backPage.find((item) => page.includes(item))) {
              console.log('登陆之前的页面设置', page)
              uni.setStorageSync('fromLoginPage', {
                page,
              })
            }

            // #ifdef H5
            uni.redirectTo({
              url: '/pages/my/yfk_login/index',
            })
            // #endif
            // #ifdef MP-WEIXIN
            uni.redirectTo({
              url: '/pages/my/login/wxLogin',
            })
            // #endif
          } else if (res.data.code === 399) {
            // 敏感词拦截
            uni.showToast({
              icon: 'none',
              title: '存在敏感不合规的词汇，请重新输入！',
            })
            resolve(res.data)
          } else if (res.data.code === 398) {
            // 敏感词拦截
            uni.showToast({
              icon: 'none',
              title: '存在敏感不合规的图片内容，请重新上传图片！',
            })
            resolve(res.data)
          } else {
            resolve(res.data)
          }
        }
      },
      fail: (err) => {
        // uni.showToast({
        //   icon: 'none',
        //   title: `${BASE_URL + options.url}请求失败`,
        // })
        reject(err)
      },
    })
  })
}

export const uploadHttp = (url, formData) => {
  console.log(url, 'uploadHttp-url')
  const token = store.state.token ? `Bearer ${store.state.token}` : ''
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: BASE_URL + '/screen/file/upload', // 仅为示例，非真实的接口地址
      filePath: url,
      name: 'file',
      formData,
      header: {
        Authorization: token,
      },
      success: (res) => {
        resolve(JSON.parse(res.data))
      },
      fail: (err) => {
        reject(err)
      },
    })
  })
}

export const csdnRequest = (options) => {
  const token = store.state.token ? `Bearer ${store.state.token}` : ''
  return new Promise((resolve, reject) => {
    uni.request({
      url: process.env.VUE_APP_BASE_API_MAP + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        Authorization: options.noToken ? '' : token,
        ...options.header,
        portToken: options.headers.portToken,
        ptid: 'PT0001',
        // 'Content-Type': options.header['Content-Type'] || 'application/json'
      },
      success: (res) => {
        if (res.statusCode === 200) {
          // 成功时 resolve 数据
          resolve(res.data)
        } else {
          // 状态码异常时 reject
          reject(new Error(`请求失败，状态码：${res.statusCode}`))
        }
      },
      fail: (err) => {
        // 网络等错误
        reject(err)
      }
    })
  })
}
