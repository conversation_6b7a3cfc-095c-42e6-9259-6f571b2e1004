<template>
  <view class="wrap">
    <view class="economy-container">
      <!-- 头部图标和标题 -->
      <view class="header">
        <view class="icon-container">
          <image src="@/static/img/home/<USER>" class="iconfont" />
        </view>
        <text class="title">环境</text>
      </view>

      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">金华市当日环境</text>
        </view>

        <view class="gauge-container">
          <view class="gauge-item">
            <view class="gauge-title">AQI</view>
            <view style="width: 100%; height: 300rpx">
              <LEchart ref="aqiGauge" />
            </view>
            <view class="gauge-quality good">优</view>
          </view>

          <view class="gauge-item">
            <view class="gauge-title">PM 2.5</view>
            <view style="width: 100%; height: 300rpx">
              <LEchart ref="pm25Gauge" />
            </view>
            <view class="gauge-quality fine">良</view>
          </view>
        </view>
      </view>

      <!-- 金华市整体环境变化趋势 -->
      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">金华市整体环境变化趋势</text>
        </view>
        <view class="chart-subtitle">
          <text class="update-time">更新日期: -</text>
        </view>
        <view style="width: 100%; height: 340rpx; margin-bottom: 10px">
          <LEchart ref="envTrendChart" />
        </view>
      </view>

      <!-- 单位GDP能耗 -->
      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">单位GDP能耗</text>
        </view>
        <view class="chart-subtitle">
          <text class="update-time">更新日期: -</text>
        </view>
        <view style="width: 100%; height: 400rpx; margin-bottom: 10px">
          <LEchart ref="gdpEnergyChart" />
        </view>
      </view>

      <!-- 区县颗粒物情况 -->
      <view class="indicator-card">
        <view class="card-header" style="justify-content: space-between">
          <view class="left">
            <image src="@/static/img/home/<USER>" class="dot" />
            <text class="card-title">区县颗粒物情况</text>
          </view>

          <view class="area-selector" @click="showAreaPicker">
            <text>{{ selectedArea }}</text>
            <view class="selector-icon">
              <text class="arrow-down">&#9660;</text>
            </view>
          </view>
        </view>

        <view class="particles-grid">
          <view class="particle-item" v-for="(item, i) in dataList" :key="i">
            <view class="flex-c-c">
              <image :src="item.icon" class="item-icon" />
              <view class="item-title">{{ item.name }}</view>
            </view>

            <view class="item-value">
              {{ item.value || '-' }}
              <text class="unit">{{ item.unit }}</text>
            </view>
            <view class="item-compare" v-if="item.name !== '风向'">
              同比:
              <text class="up">{{ item.growth || '-' }}</text>
              <image
                v-if="parseFloat(item.growth) !== 0 && item.growth !== ''"
                :src="item.growth > 0 ? upIcon : downIcon"
                style="width: 14rpx; height: 20rpx; margin-left: 8rpx"
              />
            </view>
            <view class="update-time">更新时间</view>
            <view class="update-date">{{ item.updateTime }}</view>
          </view>
        </view>
      </view>

      <!-- 区域选择弹窗 -->
      <u-popup
        :show="showAreaSelector"
        @close="showAreaSelector = false"
        mode="bottom"
        round="12"
        safe-area-inset-bottom
      >
        <view class="area-popup">
          <view class="area-popup-header">
            <text>选择区县</text>
            <view class="close-btn" @click="showAreaSelector = false">×</view>
          </view>
          <view class="area-list">
            <view
              class="area-item"
              v-for="(area, index) in areaList"
              :key="index"
              @click="selectArea(area)"
              :class="{ active: area === selectedArea }"
            >
              {{ area }}
            </view>
          </view>
        </view>
      </u-popup>
    </view>
  </view>
</template>

<script>
import * as echarts from '@/pages/shouye/components/lime-echart/static/echarts.min.js'
import LEchart from '@/pages/shouye/components/lime-echart/components/l-echart/l-echart.vue'
import { getCsdnInterface } from '@/services/csdnIndexApi/index.js'
import complexPickerAdmin from 'gdt-jsapi/complexPickerAdmin'

export default {
  components: {
    LEchart,
  },
  data() {
    return {
      upIcon: require('@/static/img/home/<USER>'),
      downIcon: require('@/static/img/home/<USER>'),
      // 环境变化趋势配置
      envTrendOption: {
        color: ['#FFD44A', '#FFA726'],
        legend: {
          data: ['AQI', 'PM 2.5'],
          top: 'top',
          left: 0,
          itemWidth: 8,
          itemHeight: 8,
          icon: 'circle',
        },
        grid: {
          top: 55,
          right: 10,
          bottom: 0,
          left: 0,
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: ['04-11', '04-12', '04-13', '04-14', '04-15', '04-16'],
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
        },
        yAxis: [
          {
            type: 'value',
            name: '',
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: '#eee',
                type: 'dashed',
              },
            },
            axisLabel: {
              color: '#999',
              fontSize: 12,
            },
          },
          {
            type: 'value',
            name: '单位：μg/m3',
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: '#eee',
                type: 'dashed',
              },
            },
            axisLabel: {
              color: '#999',
              fontSize: 12,
            },
          },
        ],
        tooltip: {
          trigger: 'axis',
        },
        series: [
          {
            name: 'AQI',
            type: 'line',
            data: [],
            symbol: 'circle',
            symbolSize: 0,
            lineStyle: {
              width: 2,
            },
          },
          {
            yAxisIndex: 1,
            name: 'PM 2.5',
            type: 'line',
            data: [],
            symbol: 'circle',
            symbolSize: 0,
            lineStyle: {
              width: 2,
            },
          },
        ],
      },
      // GDP能耗柱状图配置
      gdpEnergyOption: {
        color: ['#37A2FF'],
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}万元',
        },
        grid: {
          top: 30,
          right: 0,
          bottom: 0,
          left: 10,
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: [
            '婺城区',
            '金东区',
            '义乌市',
            '东阳市',
            '兰溪市',
            '武义县',
            '开发区',
          ],
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#333',
            fontSize: 12,
            rotate: 30,
          },
        },
        yAxis: {
          type: 'value',
          name: '单位：吨标准煤/万元',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
              type: 'dashed',
            },
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
        },
        series: {
          type: 'bar',
          barWidth: '60%',
          label: {
            show: true,
            position: 'top',
          },
          data: [],
        },
      },
      dataList: [
        {
          name: 'PM2.5',
          value: '',
          unit: 'ug/m³',
          growth: '',
          updateTime: ' ',
          icon: require('@/static/img/home/<USER>'),
        },
        {
          name: 'PM10',
          value: '',
          unit: 'ug/m³',
          growth: '',
          updateTime: ' ',
          icon: require('@/static/img/home/<USER>'),
        },
        {
          name: '大气压强',
          value: '',
          unit: 'KPa',
          growth: '',
          updateTime: ' ',
          icon: require('@/static/img/home/<USER>'),
        },
        {
          name: '温度',
          value: '',
          unit: '℃',
          growth: '',
          updateTime: ' ',
          icon: require('@/static/img/home/<USER>'),
        },
        {
          name: '湿度',
          value: '',
          unit: '%',
          growth: '',
          updateTime: ' ',
          icon: require('@/static/img/home/<USER>'),
        },
        {
          name: '噪音',
          value: '',
          unit: 'dB',
          growth: '',
          updateTime: ' ',
          icon: require('@/static/img/home/<USER>'),
        },
        {
          name: '风速',
          value: '',
          unit: 'm/s',
          growth: '',
          updateTime: ' ',
          icon: require('@/static/img/home/<USER>'),
        },
        {
          name: '风向',
          value: '',
          unit: '。',
          growth: '',
          updateTime: ' ',
          icon: require('@/static/img/home/<USER>'),
        },
      ],
      areaDatalist: [],
      // 区域选择相关
      showAreaSelector: false,
      selectedArea: '婺城区',
      areaList: [
        '婺城区',
        '开发区',
        '金东区',
        '义乌市',
        '东阳市',
        '兰溪市',
        '武义县',
        '浦江县',
        '磐安',
        '永康市',
      ],
    }
  },
  onLoad() {},
  mounted() {
    this.init()
    this.$nextTick(() => {
      this.initCharts()
    })
  },
  methods: {
    init() {
      getCsdnInterface('ldrb_hjsj').then((res) => {
        if (res.responsecode == 200) {
          this.areaDatalist = res.data
          getCsdnInterface('ldrb_hjsj2').then((res) => {
            if (res.responsecode == 200) {
              this.areaDatalist2 = res.data
              this.handleAreaData()
            }
          })
        }
      })
    },
    handleAreaData() {
      let data = this.areaDatalist.find(
        (item) => item.ssqx.slice(0, 2) == this.selectedArea.slice(0, 2)
      )
      let updateTime = ''
      this.dataList.forEach((x) => (x.value = ''))
      for (let x in data) {
        updateTime = data.gxsj.slice(0, 10)
        if (x == 'pm25') {
          this.dataList[0].value = parseFloat(data[x]).toFixed(2)
        } else if (x == 'pm10') {
          this.dataList[1].value = parseFloat(data[x]).toFixed(2)
        } else if (x == 'dqyq') {
          this.dataList[2].value = parseInt(data[x]).toFixed(2)
        } else if (x == 'wd') {
          this.dataList[3].value = parseInt(data[x])
        }
      }
      this.dataList.forEach((x) => (x.updateTime = updateTime))

      let data2 = this.areaDatalist2.find(
        (item) => item.ssqx.slice(0, 2) == this.selectedArea.slice(0, 2)
      )
      for (let x in data2) {
        if (x == 'sd') {
          this.dataList[4].value = parseFloat(data2[x]).toFixed(2)
        } else if (x == 'zs') {
          this.dataList[5].value = parseFloat(data2[x]).toFixed(2)
        } else if (x == 'fs') {
          this.dataList[6].value = parseInt(data2[x]).toFixed(2)
        } else if (x == 'fx') {
          this.dataList[7].value = parseInt(data2[x]).toFixed(2)
        }
      }
    },
    // 显示区域选择器
    showAreaPicker() {
      this.showAreaSelector = true
    },

    // 选择区域
    selectArea(area) {
      this.selectedArea = area
      this.showAreaSelector = false
      this.handleAreaData()
    },
    initCharts() {
      // 初始化AQI仪表盘
      this.$refs.aqiGauge.init(echarts, (chart) => {
        let gaugeOption = {
          series: [
            {
              type: 'gauge',
              radius: '100%',
              startAngle: 210,
              endAngle: -30,
              min: 0,
              max: 6,
              splitNumber: 6,
              axisLine: {
                lineStyle: {
                  width: 12,
                  color: [
                    [0.2, '#ff7a45'],
                    [0.4, '#ffa940'],
                    [0.6, '#bae637'],
                    [1, '#52c41a'],
                  ],
                },
              },
              pointer: {
                length: '70%',
                width: 6,
                // offsetCenter: [0, '-20%'],
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              detail: {
                show: false,
              },
              detail: {
                textStyle: {
                  color: '#000',
                  fontSize: '24',
                  fontWeight: 'bolder',
                },
              },
              data: [{ value: 0 }],
            },
          ],
        }
        chart.setOption(gaugeOption)
      })

      // 初始化PM2.5仪表盘
      this.$refs.pm25Gauge.init(echarts, (chart) => {
        let gaugeOption = {
          series: [
            {
              type: 'gauge',
              radius: '100%',
              startAngle: 210,
              endAngle: -30,
              min: 0,
              max: 6,
              splitNumber: 6,
              axisLine: {
                lineStyle: {
                  width: 12,
                  color: [
                    [0.2, '#ff7a45'],
                    [0.4, '#ffa940'],
                    [0.6, '#bae637'],
                    [1, '#52c41a'],
                  ],
                },
              },
              pointer: {
                length: '70%',
                width: 6,
                // offsetCenter: [0, '-20%'],
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              detail: {
                show: false,
              },
              detail: {
                textStyle: {
                  color: '#000',
                  fontSize: '24',
                  fontWeight: 'bolder',
                },
              },
              data: [{ value: 0 }],
            },
          ],
        }
        chart.setOption(gaugeOption)
      })

      // 初始化环境变化趋势图
      this.$refs.envTrendChart.init(echarts, (chart) => {
        chart.setOption(this.envTrendOption)
      })

      // 初始化GDP能耗图
      this.$refs.gdpEnergyChart.init(echarts, (chart) => {
        chart.setOption(this.gdpEnergyOption)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.wrap {
  min-height: 100vh;
}

.economy-container {
  box-sizing: border-box;
  border-radius: 18rpx 18rpx 0 0;
  background: url(@/static/img/home/<USER>
  background-size: 100% auto;

  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 20rpx;

    .icon-container {
      background-color: #fff;
      border-radius: 30rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20rpx;

      .iconfont {
        width: 60rpx;
        height: 60rpx;
      }
    }

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .indicator-card {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 0 25rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;

      .dot {
        width: 24rpx;
        height: 14rpx;
        margin-right: 16rpx;
      }

      .card-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }
    .chart-subtitle {
      color: #999;
      font-size: 24rpx;
      width: 100%;
      float: right;
      text-align: right;
    }

    .gauge-container {
      display: flex;
      justify-content: space-between;

      .gauge-item {
        width: 48%;
        position: relative;
        text-align: center;
        background: #ffffff rgba(0, 0, 0, 0.001);
        box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(0, 0, 0, 0.1);
        padding: 20rpx 40rpx;
        box-sizing: border-box;
        border-radius: 32rpx;

        .gauge-title {
          font-size: 34rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 10rpx;
          text-align: left;
        }

        .gauge-value {
          font-size: 48rpx;
          font-weight: bold;
          color: #333;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -20%);
        }

        .gauge-quality {
          font-size: 40rpx;
          font-weight: bold;
          margin-top: -70rpx;

          &.good {
            color: #52c41a;
          }

          &.fine {
            color: #bae637;
          }
        }
      }
      .gauge-container {
        display: flex;
        justify-content: space-between;

        .gauge-item {
          flex: 1;
          position: relative;
          text-align: center;

          .gauge-title {
            font-size: 34rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 10rpx;
          }

          .gauge-value {
            font-size: 48rpx;
            font-weight: bold;
            color: #333;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -20%);
          }

          .gauge-quality {
            font-size: 40rpx;
            font-weight: bold;
            margin-top: -20rpx;

            &.good {
              color: #52c41a;
            }

            &.fine {
              color: #bae637;
            }
          }
        }
      }
    }

    .area-selector {
      display: flex;
      align-items: center;
      background-color: #fff;
      padding: 8rpx 16rpx;
      border-radius: 12rpx;
      font-size: 26rpx;
      color: #333;
      border: #aaaaaa solid 1rpx;

      .selector-icon {
        margin-left: 20rpx;
        .arrow-down {
          font-size: 20rpx;
          color: #666;
        }
      }
    }
    // 颗粒物网格布局
    .particles-grid {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .particle-item {
        width: calc((100% - 22rpx) / 2);
        background: url('@/static/img/home/<USER>') no-repeat;
        background-size: 100% 100%;
        border-radius: 20rpx;
        padding: 20rpx;
        margin-bottom: 20rpx;
        margin-right: 22rpx;
        box-sizing: border-box;
        position: relative;
        text-align: center;
        &:nth-child(2n + 2) {
          margin-right: 0;
        }

        .item-icon {
          width: 44rpx;
          height: 44rpx;
          background-size: contain;
          background-repeat: no-repeat;
          margin-right: 20rpx;
        }

        .item-title {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 700;
          font-size: 40rpx;
          color: #333333;
        }

        .item-value {
          margin-top: 10rpx;
          font-size: 48rpx;
          font-weight: bold;
          color: #ff6b18;
          margin-bottom: 6rpx;
          font-family: D-DIN-Bold;
          .unit {
            font-size: 30rpx;
            font-weight: normal;
            margin-left: 8rpx;
          }
        }

        .item-compare {
          font-size: 32rpx;
          color: #8e98a4;
          margin-bottom: 30rpx;

          .up {
            margin-left: 8rpx;
            color: #cd2020;
          }

          .down {
            margin-left: 8rpx;
            color: #1aa269;
          }

          .arrow {
            margin-left: 8rpx;
          }
        }

        .update-time {
          font-size: 32rpx;
          color: #8e98a4;
          margin-bottom: 4rpx;
        }

        .update-date {
          font-size: 32rpx;
          color: #586779;
        }
      }
    }
  }
}
// 区域选择弹窗样式
.area-popup {
  background-color: #fff;
  padding: 30rpx;

  .area-popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1px solid #eee;
    margin-bottom: 20rpx;

    text {
      font-size: 32rpx;
      font-weight: bold;
    }

    .close-btn {
      font-size: 40rpx;
      padding: 0 20rpx;
    }
  }

  .area-list {
    display: flex;
    flex-wrap: wrap;
    padding: 20rpx 0;

    .area-item {
      width: 33.33%;
      text-align: center;
      padding: 20rpx 0;
      font-size: 28rpx;

      &.active {
        color: #0098fa;
        font-weight: bold;
      }
    }
  }
}
</style>
