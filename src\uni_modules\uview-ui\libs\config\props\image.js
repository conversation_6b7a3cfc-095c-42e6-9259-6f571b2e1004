/*
 * <AUTHOR> LQ
 * @Description  :
 * @version      : 1.0
 * @Date         : 2021-08-20 16:44:21
 * @LastAuthor   : LQ
 * @lastTime     : 2021-08-20 17:01:51
 * @FilePath     : /u-view2.0/uview-ui/libs/config/props/image.js
 */
export default {
    // image组件
    image: {
        src: '',
        mode: 'aspectFill',
        width: '300',
        height: '225',
        shape: 'square',
        radius: 0,
        lazyLoad: true,
        showMenuByLongpress: true,
        loadingIcon: 'photo',
        errorIcon: 'error-circle',
        showLoading: true,
        showError: true,
        fade: true,
        webp: false,
        duration: 500,
        bgColor: '#f3f4f6'
    }
}
