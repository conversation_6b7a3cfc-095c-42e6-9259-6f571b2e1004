<template>
  <view class="wrap">
    <view class="economy-container">
      <!-- 头部图标和标题 -->
      <view class="header">
        <view class="icon-container">
          <image src="@/static/img/home/<USER>" class="iconfont" />
        </view>
        <text class="title">经济</text>
      </view>

      <!-- 经济主要指标卡片 -->
      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">经济主要指标</text>
        </view>

        <view class="indicator-content">
          <cardRow
            :list="economicData.slice(0, 2)"
            :bg="'blue'"
            @showDetail="showDetail"
          ></cardRow>

          <cardItem
            :list="economicData.slice(2, 3)"
            @showDetail="showDetail"
          ></cardItem>

          <!-- 折线图 -->
          <view class="chart-card">
            <view class="chart-header">
              <view class="chart-title">
                工业战略性新兴产业增加值占规模以上工业增加值比重
              </view>
              <view class="chart-subtitle">更新时间：-</view>
            </view>
            <view style="width: 100%; height: 330rpx">
              <LEchart ref="lineChart" style="width: 100%; height: 100%" />
            </view>
          </view>

          <cardItem
            :list="economicData.slice(3, 4)"
            @showDetail="showDetail"
          ></cardItem>

          <cardRow
            :list="economicData.slice(4, 6)"
            :bg="'blue'"
            @showDetail="showDetail"
          ></cardRow>

          <cardItem
            :list="economicData.slice(6, 7)"
            @showDetail="showDetail"
          ></cardItem>

          <!-- 柱状图卡片 -->
          <!-- <view class="chart-card">
            <view class="chart-header">
              <view class="chart-title">产业用电量</view>
              <view class="chart-subtitle">
                <text class="update-time">更新时间：2025-5-30</text>
              </view>
            </view>
            <view style="width: 100%; height: 340rpx; margin-bottom: 10px">
              <LEchart ref="barChart" />
            </view>
          </view> -->

          <!-- 折线图卡片 -->
          <view class="chart-card">
            <view class="chart-header">
              <view class="chart-title">本月累计工业用电量</view>
              <view class="chart-subtitle">
                <text class="update-time">
                  更新时间：{{
                    lineData2.length > 0
                      ? lineData2[lineData2.length - 1].xAxis
                      : '-'
                  }}
                </text>
              </view>
            </view>
            <view style="width: 100%; height: 340rpx; margin-bottom: 10px">
              <LEchart ref="lineChart2" />
            </view>
          </view>

          <cardItem
            :list="economicData.slice(7, 8)"
            @showDetail="showDetail"
          ></cardItem>

          <!-- 饼图卡片 -->
          <view class="chart-card">
            <view style="width: 100%; height: 400rpx; position: relative">
              <LEchart ref="pieChart" />
            </view>
          </view>

          <cardItem
            :list="economicData.slice(8, 9)"
            @showDetail="showDetail"
          ></cardItem>
        </view>
      </view>
      <!-- 区县数据预览卡片 -->
      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">区县数据预览</text>
        </view>

        <view class="indicator-content">
          <!-- 社会消费品零售总额柱状图 -->
          <view class="chart-card">
            <view class="chart-header">
              <view class="chart-title">社会消费品零售总额</view>
              <view class="chart-subtitle">
                <text class="update-time">更新时间：-</text>
              </view>
            </view>
            <view style="width: 100%; height: 340rpx; margin-bottom: 10px">
              <LEchart ref="retailChart" />
            </view>
          </view>

          <!-- 金华市地图 -->
          <view class="mapTabList">
            <view
              class="mapTab"
              v-for="(x, i) in mapTabList"
              :key="i"
              :class="mapTabIndex == i ? 'mapTab_active' : ''"
              @click="changeMapTab(i)"
            >
              {{ x }}
            </view>
          </view>
          <view class="chart-card">
            <view class="chart-header">
              <view class="chart-subtitle">
                <text class="update-time">更新时间：-</text>
              </view>
            </view>
            <view style="width: 100%; height: 500rpx; margin-bottom: 10px">
              <LEchart ref="mapChart" />
            </view>
          </view>

          <!-- 高速车流量堆积条形图 -->
          <view class="chart-card">
            <view class="chart-header">
              <view class="chart-title">高速车流量</view>
              <view class="chart-subtitle" style="margin-top: 12rpx">
                <text class="update-time">
                  更新时间：{{ trafficUpdatetime }}
                </text>
              </view>
            </view>
            <view style="width: 100%; height: 500rpx; margin-bottom: 10px">
              <LEchart ref="trafficChart" />
            </view>
          </view>
        </view>
      </view>

      <!-- 高频经济社会指标监测数据 -->
      <view class="indicator-card" style="padding: 10px">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">高频经济社会指标监测数据</text>
        </view>
        <jjTable @lastDate="getJjTableLastDate"></jjTable>
      </view>

      <!-- 指标弹窗 -->
      <view v-if="show">
        <popup
          :show="show"
          :detailInfo="detailInfo"
          @close="show = false"
        ></popup>
      </view>
    </view>
  </view>
</template>

<script>
import * as echarts from '@/pages/shouye/components/lime-echart/static/echarts.min.js'
import LEchart from '@/pages/shouye/components/lime-echart/components/l-echart/l-echart.vue'
import cardRow from '@/pages/shouye/components/cardRow.vue'
import cardItem from '@/pages/shouye/components/card.vue'
import popup from '@/pages/shouye/components/indexPop.vue'
import { getCsdnInterface } from '@/services/csdnIndexApi/index.js'
import jjTable from '@/pages/ribaoDetails/sskb/jj_table.vue'

export default {
  components: { LEchart, cardRow, cardItem, popup, jjTable },
  data() {
    return {
      economicData: [
        {
          title: '地区生产总值',
          value: '', //1675.93
          unit: '亿元',
          growthLabel: '增速',
          growth: '', //6.6
          updateTime: '', //2025-5-30
        },
        {
          title: '一般公共预算收入',
          value: '', //197.90
          unit: '亿元',
          growthLabel: '增速',
          growth: '', //3.2
          updateTime: '', //2025-5-30
        },
        {
          title: '规上工业增加值',
          value: '',
          unit: '亿元',
          growthLabel: '同比',
          growth: '', //10.2
          updateTime: '', //2025-5-30
        },
        {
          title: '在册市场主体数',
          value: '',
          unit: '家',
          growthLabel: '',
          growth: '',
          updateTime: '2025-6-30',
        },
        {
          title: '当日新增市场主体数',
          value: '',
          unit: '家',
          growthLabel: '同比',
          growth: '',
          updateTime: '2025-6-30',
        },
        {
          title: '当日注销市场主体数',
          value: '',
          unit: '家',
          growthLabel: '同比',
          growth: '',
          updateTime: '2025-6-30',
        },
        {
          title: '全社会用电量',
          value: '', //540739
          unit: '万千瓦',
          growthLabel: '同比',
          growth: '', //7.0
          updateTime: '', //2025-5-30
        },
        {
          title: '固定资产投资',
          value: '',
          unit: '万元',
          growthLabel: '同比',
          growth: '', //6.3
          updateTime: '', //2025-5-30
        },
        {
          title: '民间投资占固定资产投资比重',
          value: '',
          unit: '%',
          growthLabel: '同比',
          growth: '',
          updateTime: '',
        },
      ],
      upIcon: require('@/static/img/home/<USER>'),
      downIcon: require('@/static/img/home/<USER>'),
      // 折线图数据
      lineData: {
        // xAxis: [
        //   '2023-11',
        //   '2023-12',
        //   '2024-01',
        //   '2024-02',
        //   '2024-03',
        //   '2024-04',
        // ],
        // values: [50, 60, 40, 80, 50, 70],
      },
      lineOptions: {
        grid: {
          top: 30,
          right: 26,
          bottom: 10,
          left: 10,
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: '',
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
          },
        },
        yAxis: {
          type: 'value',
          name: '',
          nameTextStyle: {
            padding: [0, 0, 0, 30],
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
            },
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
          },
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}:  {c}',
        },
        series: [
          {
            data: '',
            type: 'line',
            symbolSize: 0,
            lineStyle: {
              width: 2,
              color: '#2979ff',
            },
            itemStyle: {
              color: '#2979ff',
            },
            areaStyle: {
              color: '',
            },
          },
        ],
      },
      // 柱状图数据
      barData: [
        // { name: '第一产业', value: 2731 },
        // { name: '第二产业', value: 364702 },
        // { name: '第三产业', value: 87390 },
      ],
      // 折线图数据
      lineData2: {
        // xAxis: [
        // ],
        // values: [250, 300, 200, 410, 230, 350],
      },
      // 饼图数据
      pieData: [
        // { name: '制造业投资', value: 76 },
        // { name: '高新技术投资', value: 24 },
        // { name: '民间投资', value: 24 },
        // { name: '其他', value: 24 },
      ],
      // 柱状图配置
      barOptions: {
        color: ['#FFD44A'],
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}万度',
        },
        grid: {
          top: 30,
          right: 0,
          bottom: 10,
          left: 0,
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: '',
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
          },
        },
        yAxis: {
          type: 'value',
          name: '单位:万度',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
              type: 'dashed',
            },
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
        },
        series: {
          type: 'bar',
          barWidth: '40%',
          data: [],
          label: {
            show: true,
            position: 'top',
            formatter: '{c}万度',
          },
        },
      },
      // 饼图配置
      pieOptions: {
        color: ['#1890FF', '#4CD1A7', '#3366CC', '#FFD44A'],
        title: {
          text: '',
          top: '28%',
          left: '0%',
          textStyle: {
            rich: {
              name: {
                width: 190,
                fontSize: 14,
                fontWeight: 'normal',
                color: '#666666',
                padding: [10, 0],
                align: 'center',
              },
              val: {
                width: 190,
                fontSize: 32,
                fontWeight: 'bold',
                color: '#333333',
                align: 'center',
              },
            },
          },
        },
        legend: {
          right: 28,
          top: 'center',
          orient: 'vertical',
        },
        series: {
          type: 'pie',
          radius: ['50%', '80%'],
          center: ['30%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'inside',
            formatter: function (params) {
              return params.value + '%'
            },
          },
          labelLine: {
            show: false,
          },
          data: [],
        },
      },
      // 社会消费品零售总额数据
      retailData: [
        { name: '婺城区', value: 0 }, //923852
        { name: '开发区', value: 0 }, //713079
        { name: '金东区', value: 0 }, //1114849
        { name: '兰溪市', value: 0 }, //623546
        { name: '东阳市', value: 0 }, //1319314
        { name: '义乌市', value: 0 }, //4562421
        { name: '永康市', value: 0 }, //1550473
        { name: '浦江县', value: 0 }, //419659
        { name: '武义县', value: 0 }, //602418
        { name: '磐安县', value: 0 }, //212034
      ],
      // 金华市地图数据
      mapTabList: ['商业客流量', '新房销售量', '二手房销售量'],
      mapTabIndex: 0,
      mapData: [
        { name: '婺城区', value: '' },
        { name: '金东区', value: '' },
        { name: '义乌市', value: '' },
        { name: '东阳市', value: '' },
        { name: '兰溪市', value: '' },
        { name: '浦江县', value: '' },
        { name: '永康市', value: '' },
        { name: '磐安县', value: '' },
        { name: '武义县', value: '' },
      ],
      // 高速车流量数据
      trafficData: [],
      // 社会消费品零售总额图表配置
      retailOptions: {
        color: ['#37A2FF'],
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}万元',
        },
        grid: {
          top: 30,
          right: 20,
          bottom: 10,
          left: 0,
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#333',
            fontSize: 12,
            interval: 0,
            rotate: 30,
          },
        },
        yAxis: {
          type: 'value',
          name: '单位：万元',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
              type: 'dashed',
            },
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
        },
        series: {
          type: 'bar',
          barWidth: '60%',
          data: [],
        },
      },
      // 金华市地图配置
      mapOptions: {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}',
        },
        series: [
          {
            name: '金华市',
            type: 'map',
            map: 'jinhua',
            roam: false,
            zoom: 1.2,
            selectedMode: false,
            label: {
              show: true,
              color: '#fff',
              formatter: function (params) {
                return params.name + '\n' + params.value
              },
            },
            itemStyle: {
              areaColor: '#21A6FD',
              borderColor: '#fff',
              borderWidth: 1.5,
            },
            emphasis: {
              disabled: true,
            },
            data: [],
          },
        ],
      },
      // 高速车流量配置
      trafficOptions: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: function (params) {
            var tar0 = params[0]
            var tar1 = params[1]
            return (
              tar0.name +
              '\n' +
              tar0.seriesName +
              ': ' +
              tar0.value +
              '辆\n' +
              tar1.seriesName +
              ': ' +
              tar1.value +
              '辆'
            )
          },
        },
        legend: {
          data: ['进口总量', '出口总量'],
          left: 10,
          top: 0,
          itemWidth: 12,
        },
        grid: {
          top: 30,
          right: 30,
          bottom: 10,
          left: 0,
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
              type: 'dashed',
            },
          },
        },
        yAxis: {
          type: 'category',
          data: [],
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#333',
          },
        },
        series: [
          {
            name: '进口总量',
            type: 'bar',
            stack: 'total',
            color: '#2196F3',
            label: {
              show: true,
              position: 'inside',
              color: '#fff',
            },
            data: [],
          },
          {
            name: '出口总量',
            type: 'bar',
            stack: 'total',
            color: '#4CD1A7',
            label: {
              show: true,
              position: 'inside',
              color: '#fff',
            },
            data: [],
          },
        ],
      },
      trafficUpdatetime: '',
      lastDate: '',
      jjTableLastDate: '',
      //
      popupList: [],
      show: false,
      detailInfo: {},
    }
  },
  mounted() {
    this.lastDate = this.getLastDate()
    this.init()
    this.initCharts()
  },
  onShow() {},
  methods: {
    init() {
      //市场主体数
      getCsdnInterface('qyhx_scgk_qxld').then((res) => {
        if (res.responsecode == 200) {
          this.economicData[3].value = res.data[0].tjz
        }
      })
      getCsdnInterface('ldrb_drxzsczts').then((res) => {
        if (res.responsecode == 200) {
          this.economicData[4].value = res.data[0].tjz
          this.economicData[4].growth = res.data[1]
            ? parseFloat(res.data[1].tjz).toFixed(2) + '%'
            : ''
          this.economicData[4].updateTime = res.data[2]?.tjz || ''
        }
      })
      getCsdnInterface('ldrb_zxsczts').then((res) => {
        if (res.responsecode == 200) {
          this.economicData[5].value = res.data[0]?.tjz || ''
          this.economicData[5].growth = res.data[1]
            ? parseFloat(res.data[1].tjz).toFixed(2) + '%'
            : ''
          this.economicData[5].updateTime = res.data[2]?.tjz || ''
        }
      })
      //全社会用电量
      let promises = []
      for (let i = 0; i < 7; i++) {
        let date = new Date(new Date().getTime() - 1000 * 60 * 60 * 24 * i)
        let date_str =
          new Date(date).getFullYear().toString() +
          '-' +
          (new Date(date).getMonth() + 1 > 10
            ? new Date(date).getMonth() + 1
            : '0' + (new Date(date).getMonth() + 1)) +
          '-' +
          (new Date(date).getDate() > 10
            ? new Date(date).getDate()
            : '0' + new Date(date).getDate())
        promises.push(
          getCsdnInterface('ldrb_qshydltj', { stat_date: date_str }).then(
            (res) => {
              if (res.responsecode == 200 && res.data.length > 0) {
                return res
              }
              throw new Error('No valid data')
            }
          )
        )
      }
      Promise.any(promises)
        .then((res) => {
          // 处理数据
          this.economicData[6].value = res.data[0].daily_usage
          this.economicData[6].growth = res.data[0].daily_yoy
          this.economicData[6].updateTime = res.data[0].stat_date
        })
        .catch((error) => {
          console.error('No valid data found:', error)
        })

      //工业用电量折线图
      getCsdnInterface('ldrb_qshydltj').then((res) => {
        if (res.responsecode == 200) {
          this.lineData2 = res.data.map((item) => {
            return {
              xAxis: item.stat_date,
              values: item.industry_monthly_usage,
            }
          })
          // 初始化折线图
          this.$refs.lineChart2.init(echarts, (chart) => {
            let option = JSON.parse(JSON.stringify(this.lineOptions))
            option.xAxis.data = this.lineData2.map((x) => x.xAxis)
            option.yAxis.name = '单位：亿千瓦时'
            option.series[0].data = this.lineData2.map((x) => x.values)
            let color = {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(255, 167, 38, 0.3)',
                },
                {
                  offset: 1,
                  color: 'rgba(255, 167, 38, 0.05)',
                },
              ],
            }
            option.series[0].areaStyle.color = color
            option.series[0].lineStyle.color = '#FFA726'
            option.series[0].itemStyle.color = '#FFA726'
            chart.setOption(option)
          })
        }
      })

      //高速车流量
      getCsdnInterface('ldrb_gscll').then((res) => {
        if (res.responsecode == 200) {
          this.trafficUpdatetime = res.data[0].en_date || this.lastDate
          this.trafficData = res.data.map((item) => {
            return {
              name: item.ssqx,
              value1: item.rkzl,
              value2: item.ckzl,
            }
          })
          // 初始化高速车流量图表
          this.$refs.trafficChart.init(echarts, (chart) => {
            this.trafficOptions.yAxis.data = this.trafficData.map(
              (item) => item.name
            )
            this.trafficOptions.series[0].data = this.trafficData.map(
              (item) => item.value1
            )
            this.trafficOptions.series[1].data = this.trafficData.map(
              (item) => item.value2
            )
            chart.setOption(this.trafficOptions)
          })
        }
      })
      //手动更新部分指标的updateTime
      this.economicData[4].updateTime = this.lastDate
      this.economicData[5].updateTime = this.lastDate
      this.economicData[6].updateTime = this.lastDate
      this.$forceUpdate()

      //弹窗详情
      getCsdnInterface('ldrb_tcsj').then((res) => {
        if (res.responsecode == 200) {
          this.popupList = res.data
        }
      })
    },
    changeMapTab(i) {
      this.mapTabIndex = i

      // 初始化金华市地图
      this.$refs.mapChart.init(echarts, (chart) => {
        echarts.registerMap('jinhua', require('@/static/json/jinhua.json'))
        this.mapOptions.series[0].data = this.mapData
        chart.setOption(this.mapOptions)
      })
    },
    showDetail(item) {
      console.log(item)
      if (
        this.popupList.length > 0 &&
        this.popupList.find((x) => (x.indicator_name = item.title))
      ) {
        let obj = this.popupList.find((x) => (x.indicator_name = item.title))
        this.detailInfo = Object.assign({}, obj, item)
        this.show = true
      }
    },
    initCharts() {
      this.$refs.lineChart.init(echarts, (e) => {
        this.lineOptions.xAxis.data = this.lineData.xAxis
        this.lineOptions.series[0].data = this.lineData.values
        let color = {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(41, 121, 255, 0.3)',
            },
            {
              offset: 1,
              color: 'rgba(41, 121, 255, 0.05)',
            },
          ],
        }
        this.lineOptions.series[0].areaStyle.color = color

        e.setOption(this.lineOptions)
      })
      // 初始化柱状图
      // this.$refs.barChart.init(echarts, (chart) => {
      //   this.barOptions.xAxis.data = this.barData.map((item) => item.name)
      //   this.barOptions.series.data = this.barData.map((item) => item.value)
      //   chart.setOption(this.barOptions)
      // })

      // 初始化饼图
      this.$refs.pieChart.init(echarts, (chart) => {
        let total = 0
        this.pieOptions.series.data = this.pieData
        this.pieOptions.title.text = '{name|总数}\n{val|' + total + '}'
        chart.setOption(this.pieOptions)
      })
      // 初始化社会消费品零售总额图表
      this.$refs.retailChart.init(echarts, (chart) => {
        this.retailOptions.xAxis.data = this.retailData.map((item) => item.name)
        this.retailOptions.series.data = this.retailData.map(
          (item) => item.value
        )
        chart.setOption(this.retailOptions)
      })

      // 初始化金华市地图
      this.$refs.mapChart.init(echarts, (chart) => {
        echarts.registerMap('jinhua', require('@/static/json/jinhua.json'))

        this.mapOptions.series[0].data = this.mapData
        chart.setOption(this.mapOptions)
      })
    },
    getJjTableLastDate(e) {
      this.jjTableLastDate = e
    },
    getLastDate() {
      let lastDate = new Date(new Date().getTime() - 60 * 60 * 24 * 1000)
      let dateStr =
        lastDate.getFullYear() +
        '-' +
        (lastDate.getMonth() + 1) +
        '-' +
        lastDate.getDate()
      return dateStr
    },
  },
}
</script>

<style lang="scss" scoped>
.wrap {
  min-height: 100vh;
}

.economy-container {
  box-sizing: border-box;
  border-radius: 18rpx 18rpx 0 0;
  background: url(@/static/img/home/<USER>
  background-size: 100% auto;

  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 20rpx;

    .icon-container {
      background-color: #fff;
      border-radius: 30rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20rpx;

      .iconfont {
        width: 60rpx;
        height: 60rpx;
      }
    }

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .indicator-card {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 0 25rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;

      .dot {
        width: 24rpx;
        height: 14rpx;
        margin-right: 16rpx;
      }

      .card-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .indicator-content {
      .chart-card {
        background-color: #fff;
        border-radius: 12rpx;
        margin: 15rpx 0;

        .chart-header {
          margin-bottom: 20rpx;

          .chart-title {
            font-size: 36rpx;
            font-weight: 500;
            color: #2c3542;
            margin-bottom: 15rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: center;
          }

          .chart-subtitle {
            color: #999;
            font-size: 24rpx;
            width: 100%;
            float: right;
            text-align: right;
          }
        }

        .chart-box {
          width: 100%;
          height: 400rpx;
        }
      }
    }
  }

  .mapTabList {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .mapTab {
      border-radius: 32rpx;
      padding: 10rpx 34rpx;
      box-sizing: border-box;
      border: 1rpx solid #aaaaaa;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 26rpx;
      color: #222222;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .mapTab_active {
      background-color: #0098fa;
      border-color: #0098fa;
      color: #fff;
    }
  }
}
</style>
