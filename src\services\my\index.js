import { http } from '@/utils/http'

export const getMyList = (data) => {
  const params = {
    url: `/screen/myApplication/list`,
    method: 'GET',
    data: {
      fl: '我的服务',
    },
  }
  return http(params)
}

//删除用户
export const deleteUser = (userIds) => {
  const params = {
    url: `/screen/ajhUser/${userIds}`,
    method: 'delete',
  }
  return http(params)
}

// 社区画像-获取距离最近的社区信息
export const getMinDistanceSq = (data) => {
  const params = {
    url: `/screen/sqhx/getMinDistanceSq`,
    method: 'GET',
    data,
  }
  return http(params)
}

// 社区画像-获取社区下拉树列表
export const getSqTreeselect = (data) => {
  const params = {
    url: `/screen/sqhx/treeselect`,
    method: 'GET',
    data,
  }
  return http(params)
}
// 社区画像-带参数社区查询
export const getSqOptions = (data) => {
  const params = {
    url: `/screen/sqhx/getSq`,
    method: 'GET',
    data,
  }
  return http(params)
}
