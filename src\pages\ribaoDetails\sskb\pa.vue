<template>
  <view class="wrap">
    <view class="economy-container">
      <!-- 头部图标和标题 -->
      <view class="header">
        <view class="icon-container">
          <image src="@/static/img/home/<USER>" class="iconfont" />
        </view>
        <text class="title">平安</text>
      </view>

      <!-- 警情数量统计（饼图） -->
      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">警情数量统计</text>
        </view>
        <view class="chart-container">
          <view style="width: 100%; height: 350rpx; position: relative">
            <LEchart ref="policeStatsPieChart" />
          </view>
        </view>
      </view>

      <!-- 警情数量统计（折线图） -->
      <view class="indicator-card">
        <view class="card-header" style="justify-content: space-between">
          <view class="left">
            <image src="@/static/img/home/<USER>" class="dot" />
            <text class="card-title">警情数量变化趋势</text>
          </view>
          <view class="area-selector" @click="showAreaPicker(1)">
            <text>{{ selectedArea1 }}</text>
            <view class="selector-icon">
              <text class="arrow-down">&#9660;</text>
            </view>
          </view>
        </view>
        <view style="width: 100%; height: 340rpx; margin-bottom: 10px">
          <LEchart ref="policeStatsLineChart" />
        </view>
      </view>

      <!-- 电信诈骗类型分布 -->
      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">电信诈骗类型分布</text>
        </view>
        <view class="chart-container">
          <view style="width: 100%; height: 400rpx; position: relative">
            <LEchart ref="fraudTypePieChart" />
          </view>
        </view>
      </view>

      <!-- 电信诈骗情况统计 -->
      <view class="indicator-card">
        <view class="card-header" style="justify-content: space-between">
          <view class="left">
            <image src="@/static/img/home/<USER>" class="dot" />
            <text class="card-title">电信诈骗情况统计</text>
          </view>
          <view class="area-selector" @click="showAreaPicker(2)">
            <text>{{ selectedArea2 }}</text>
            <view class="selector-icon">
              <text class="arrow-down">&#9660;</text>
            </view>
          </view>
        </view>
        <view style="width: 100%; height: 340rpx; margin-bottom: 10px">
          <LEchart ref="fraudStatsBarChart" />
        </view>
      </view>

      <!-- 12345热线投诉咨询数量（按时间） -->
      <view class="indicator-card">
        <view class="card-header" style="justify-content: space-between">
          <view class="left">
            <image src="@/static/img/home/<USER>" class="dot" />
            <text class="card-title">信访批次/人次</text>
          </view>
        </view>
        <view style="width: 100%; height: 340rpx; margin-bottom: 10px">
          <LEchart ref="LetterVisitsChart" />
        </view>
      </view>

      <!-- 12345热线投诉咨询数量（按地区） -->
      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">12345热线投诉咨询数量</text>
        </view>
        <view style="width: 100%; height: 500rpx; margin-bottom: 10px">
          <LEchart ref="hotlineBarByRegionChart" />
        </view>
      </view>

      <!-- 区域选择弹窗 -->
      <u-popup
        :show="showAreaSelector"
        @close="showAreaSelector = false"
        mode="bottom"
        round="12"
        safe-area-inset-bottom
      >
        <view class="area-popup">
          <view class="area-popup-header">
            <text>选择区县</text>
            <view class="close-btn" @click="showAreaSelector = false">×</view>
          </view>
          <view class="area-list">
            <view
              class="area-item"
              v-for="(area, index) in areaList"
              :key="index"
              @click="selectArea(area)"
              :class="{
                active:
                  (area == selectedArea1 && pickerNum == 1) ||
                  (area == selectedArea2 && pickerNum == 2) ||
                  (area == selectedArea3 && pickerNum == 3),
              }"
            >
              {{ area }}
            </view>
          </view>
        </view>
      </u-popup>
    </view>
  </view>
</template>

<script>
import * as echarts from '@/pages/shouye/components/lime-echart/static/echarts.min.js'
import LEchart from '@/pages/shouye/components/lime-echart/components/l-echart/l-echart.vue'
import { getCsdnInterface } from '@/services/csdnIndexApi/index.js'
import axios from 'axios'
export default {
  components: {
    LEchart,
  },
  data() {
    return {
      // 区域选择相关
      pickerNum: 0,
      showAreaSelector: false,
      selectedArea1: '金华市',
      selectedArea2: '金华市',
      selectedArea3: '金华市',
      areaList: [
        '金华市',
        '婺城区',
        '金东区',
        '义乌市',
        '东阳市',
        '兰溪市',
        '武义县',
        '浦江县',
        '磐安县',
        '永康市',
      ],
      policePieData: [
        { name: '110', value: 0 },
        { name: '119', value: 0 },
        { name: '122', value: 0 },
        { name: '综合接警', value: 0 },
      ],
      policePieOption: {
        color: ['#1890FF', '#4CD1A7', '#3366CC', '#FFD44A'],
        title: {
          text: '',
          top: '26%',
          left: '0%',
          textStyle: {
            rich: {
              name: {
                width: 190,
                fontSize: 14,
                fontWeight: 'normal',
                color: '#666666',
                padding: [10, 0],
                align: 'center',
              },
              val: {
                width: 190,
                fontSize: 32,
                fontWeight: 'bold',
                color: '#333333',
                align: 'center',
              },
            },
          },
        },
        legend: {
          right: 28,
          top: 'center',
          orient: 'vertical',
        },
        series: {
          type: 'pie',
          radius: ['50%', '80%'],
          center: ['30%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'inside',
            formatter: function (params) {
              return params.value + '%'
            },
          },
          labelLine: {
            show: false,
          },
          data: [],
        },
      },
      policeStatsLineData: [],
      policeStatsLineOption: {
        color: ['#1890FF', '#4CD1A7', '#3366CC', '#FFD44A'],
        grid: {
          top: 30,
          right: 0,
          bottom: 0,
          left: 0,
          containLabel: true,
        },
        legend: {
          right: 0,
          icon: 'circle',
          itemWidth: 8,
        },
        xAxis: {
          type: 'category',
          data: [
            '202411',
            '202412',
            '202501',
            '202502',
            '202503',
            '202504',
            '202505',
          ],
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 8000,
          interval: 2000,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
              type: 'dashed',
            },
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
        },
        tooltip: {
          trigger: 'axis',
        },
        series: [
          {
            name: '110',
            type: 'line',
            data: [],
            symbolSize: 0,
            lineStyle: {
              width: 2,
            },
          },
          {
            name: '119',
            type: 'line',
            data: [],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2,
            },
          },
          {
            name: '122',
            type: 'line',
            data: [],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2,
            },
          },
          {
            name: '综合接警',
            type: 'line',
            data: [],
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2,
            },
          },
        ],
      },
      fraudTypePieData: [
        { name: '投资理财', value: 0 },
        { name: '网贷', value: 0 },
        { name: '裸聊', value: 0 },
        { name: '其他', value: 0 },
      ],
      // 电信诈骗情况统计
      fraudStatsBarOption: {
        color: ['#1890FF', '#4CD1A7'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          top: 30,
          right: 0,
          bottom: 0,
          left: 0,
          containLabel: true,
        },
        legend: {
          icon: 'square',
          itemWidth: 8,
          itemHeight: 8,
          left: 'center',
        },
        xAxis: {
          type: 'category',
          data: [
            '202411',
            '202412',
            '202501',
            '202502',
            '202503',
            '202504',
            '202505',
          ],
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
        },
        yAxis: [
          {
            type: 'value',
            name: '次',
            min: 0,
            max: 500,
            interval: 100,
            position: 'left',
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: '#eee',
                type: 'dashed',
              },
            },
            axisLabel: {
              color: '#999',
              fontSize: 12,
            },
          },
          {
            type: 'value',
            name: '万元',
            min: 0,
            max: 100,
            interval: 20,
            position: 'right',
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              color: '#999',
              fontSize: 12,
            },
          },
        ],
        series: [
          {
            name: '诈骗次数',
            type: 'bar',
            data: [],
            barWidth: '20%',
            yAxisIndex: 0,
          },
          {
            name: '诈骗金额',
            type: 'bar',
            data: [],
            barWidth: '20%',
            yAxisIndex: 1,
          },
        ],
      },
      // 信访批次/人数
      letterVisitsData: [],
      letterVisitsOption: {
        color: ['#1890FF', '#4CD1A7'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          top: 32,
          right: 4,
          bottom: 0,
          left: 8,
          containLabel: true,
        },
        legend: {
          itemWidth: 12,
          itemHeight: 8,
          left: 'center',
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
        },
        yAxis: [
          {
            type: 'value',
            name: '单位：批次',
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: '#eee',
                type: 'dashed',
              },
            },
            axisLabel: {
              color: '#999',
              fontSize: 12,
            },
          },
          {
            type: 'value',
            name: '单位：人次',
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: '#eee',
                type: 'dashed',
              },
            },
            axisLabel: {
              color: '#999',
              fontSize: 12,
            },
          },
        ],
        tooltip: {
          trigger: 'axis',
        },
        series: [
          {
            name: '信访批次',
            type: 'line',
            data: [],
            symbol: 'circle',
            symbolSize: 0,
            lineStyle: {
              width: 2,
            },
          },
          {
            yAxisIndex: 1,
            name: '信访人数',
            type: 'bar',
            barWidth: '30%',
            data: [],
          },
        ],
      },
      data12345: [],
      // 12345热线投诉咨询数量（按地区）
      hotlineBarByRegionOption: {
        color: ['#1890FF', '#4CD1A7'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          top: 30,
          right: 18,
          bottom: 0,
          left: 0,
          containLabel: true,
        },
        legend: {
          icon: 'square',
          itemWidth: 8,
          itemHeight: 8,
          left: 'center',
        },
        xAxis: {
          type: 'value',
          max: 1000,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
              type: 'dashed',
            },
          },
        },
        yAxis: {
          type: 'category',
          data: [
            '婺城区',
            '永康市',
            '浦江县',
            '金东区',
            '兰溪市',
            '磐安县',
            '武义县',
            '东阳市',
            '义乌市',
          ],
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
          },
        },
        series: [
          {
            name: '投诉数量',
            type: 'bar',
            stack: 'total',
            data: [],
            label: {
              show: true,
              position: 'inside',
              color: '#fff',
            },
          },
          {
            name: '咨询数量',
            type: 'bar',
            stack: 'total',
            data: [],
            label: {
              show: true,
              position: 'inside',
              color: '#fff',
            },
          },
        ],
      },
      hotlineBarByRegionOption2: {
        color: ['#0098FA', '#0CD9B5'],
        legend: {
          data: ['投诉数量', '信访数量'],
          top: 'top',
          right: 0,
          itemWidth: 8,
          itemHeight: 8,
          icon: 'circle',
        },
        grid: {
          top: 55,
          right: 8,
          bottom: 0,
          left: 10,
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
        },
        yAxis: [
          {
            type: 'value',
            name: '单位:次',
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: '#eee',
                type: 'dashed',
              },
            },
            axisLabel: {
              color: '#999',
              fontSize: 12,
            },
          },
        ],
        tooltip: {
          trigger: 'axis',
        },
        series: [
          {
            type: 'bar',
            data: [],
            barWidth: '30%',
          },
          // {
          //   type: 'line',
          //   data: [],
          //   symbol: 'circle',
          //   symbolSize: 0,
          //   lineStyle: {
          //     width: 2,
          //   },
          //   areaStyle: {
          //     color: {
          //       type: 'linear',
          //       x: 0,
          //       y: 0,
          //       x2: 0,
          //       y2: 1,
          //       colorStops: [
          //         {
          //           offset: 0,
          //           color: 'rgba(12, 217, 181, 0.3)',
          //         },
          //         {
          //           offset: 1,
          //           color: 'rgba(12, 217, 181, 0.05)',
          //         },
          //       ],
          //     },
          //   },
          // },
        ],
      },
    }
  },
  onLoad() {},
  mounted() {
    this.init()
    this.$nextTick(() => {
      this.initCharts()
    })
  },
  methods: {
    init() {
      // 信访批次/人数
      getCsdnInterface('ldrb_xfpcjrs').then((res) => {
        if (res.responsecode == 200) {
          this.letterVisitsData = res.data.map((item) => {
            return {
              name: item.ywwd2,
              value1: item.tjz,
              value2: item.ywwd1,
            }
          })

          this.$refs.LetterVisitsChart.init(echarts, (chart) => {
            this.letterVisitsOption.xAxis.data = this.letterVisitsData.map(
              (item) => item.name
            ).reverse()
            this.letterVisitsOption.series[0].data = this.letterVisitsData.map(
              (item) => item.value1
            ).reverse()
            this.letterVisitsOption.series[1].data = this.letterVisitsData.map(
              (item) => item.value2
            ).reverse()
            chart.setOption(this.letterVisitsOption)
          })
        }
      })

      //12345
      this.get12345Data()
    },
    async get12345Data() {
      const res1 = await axios({
        method: 'GET',
        url: '/csdnMap/adm-api/mis/irs/getLast7DaysReportData',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8',
          Authorization: uni.getStorageSync('csdnIndexApiAuthorization'),
        },
      })
      this.data12345 = res1.data.data.map((item) => ({
        value: item.day_count,
        name: item.day_time,
      }))
      this.$nextTick(() => {
        // 12345热线投诉咨询数量
        this.$refs.hotlineBarByRegionChart.init(echarts, (chart) => {
          this.hotlineBarByRegionOption2.xAxis.data = this.data12345
            .map((x) => x.name)
            .reverse()
          this.hotlineBarByRegionOption2.series[0].data = this.data12345
            .map((x) => x.value)
            .reverse()
          chart.setOption(this.hotlineBarByRegionOption2)
        })
      })
      // console.log(res1)
    },
    // async get12345Data() {
    //   let i = 0
    //   this.data12345 = [
    //     { name: '', value: 0 },
    //     { name: '', value: 0 },
    //     { name: '', value: 0 },
    //     { name: '', value: 0 },
    //     { name: '', value: 0 },
    //     { name: '', value: 0 },
    //     { name: '', value: 0 },
    //   ]
    //   for (i; i < 7; i++) {
    //     const idx = i
    //     let date = new Date(new Date().getTime() - 1000 * 60 * 60 * 24 * i)
    //     let date_str = this.getFormattedTime(date)
    //     let date_str_with_ = this.getFormattedTime(date, 1)
    //     this.data12345[idx].name = date_str
    //     //获取义乌地区的数据
    //     const res1 = await new Promise((r, e) => {
    //       uni.request({
    //         url: '/ywApi/12345fenlei@1.0',
    //         data: {
    //           createtime: date_str_with_,
    //         },
    //         method: 'GET', // 明确指定请求方法
    //         success: r,
    //         fail: e,
    //       })
    //     })
    //     let sum1 = 0
    //     res1.data.datas.forEach((x) => (sum1 += parseInt(x.count || 0)))
    //     this.data12345[i].value += parseInt(sum1)
    //     // console.log(this.data12345)

    //     //获取义乌地区之外的数据
    //     const res2 = await axios({
    //       method: 'post',
    //       url: '/csdnMap/adm-api/mis/irs/actuator',
    //       headers: {
    //         'Content-Type': 'application/json; charset=UTF-8',
    //         Authorization: uni.getStorageSync('csdnIndexApiAuthorization'),
    //       },
    //       data: {
    //         name: '除义乌其他区域接口',
    //         url: 'http://dw.jinhua.gov.cn/gateway/api/001008007012073/dataSharing/Y1cn4f9P3xd19Wc4.htm',
    //         params: { countDay: date_str },
    //       },
    //     })
    //     let sum2 = 0
    //     res2.data.datas.dataList.forEach(
    //       (x) => (sum2 += parseInt(x.totalCount || 0))
    //     )
    //     this.data12345[i].value += parseInt(sum2)
    //     console.log(this.data12345)
    //   }
    //   this.$nextTick(() => {
    //     // 12345热线投诉咨询数量
    //     this.$refs.hotlineBarByRegionChart.init(echarts, (chart) => {
    //       this.hotlineBarByRegionOption2.xAxis.data = this.data12345
    //         .map((x) => x.name)
    //         .reverse()
    //       this.hotlineBarByRegionOption2.series[0].data = this.data12345
    //         .map((x) => x.value)
    //         .reverse()
    //       chart.setOption(this.hotlineBarByRegionOption2)
    //     })
    //   })
    // },
    // 显示区域选择器
    showAreaPicker(num) {
      this.pickerNum = num
      this.showAreaSelector = true
    },

    // 选择区域
    selectArea(area) {
      if (this.pickerNum == 1) {
        this.selectedArea1 = area
        //获取数据
      } else if (this.pickerNum == 2) {
        this.selectedArea2 = area
      } else if (this.pickerNum == 3) {
        this.selectedArea3 = area
      }
      this.showAreaSelector = false
      // this.initCharts()
    },
    // 初始化所有图表
    initCharts() {
      // 警情数量统计饼图
      this.$refs.policeStatsPieChart.init(echarts, (chart) => {
        let total = 0
        this.policePieOption.series.data = this.policePieData
        this.policePieOption.title.text = '{name|总数}\n{val|' + total + '}'
        chart.setOption(this.policePieOption)
      })

      // 警情数量统计折线图
      this.$refs.policeStatsLineChart.init(echarts, (chart) => {
        chart.setOption(this.policeStatsLineOption)
      })

      // 电信诈骗类型分布
      this.$refs.fraudTypePieChart.init(echarts, (chart) => {
        let total = 0
        this.policePieOption.series.data = this.fraudTypePieData
        this.policePieOption.title.text = '{name|总数}\n{val|' + total + '}'
        chart.setOption(this.policePieOption)
      })

      // 电信诈骗情况统计
      this.$refs.fraudStatsBarChart.init(echarts, (chart) => {
        chart.setOption(this.fraudStatsBarOption)
      })
    },
    getFormattedTime(date, type) {
      if (type == 1) {
        //需要下划线
        return (
          new Date(date).getFullYear().toString() +
          '-' +
          (new Date(date).getMonth() + 1 > 10
            ? new Date(date).getMonth() + 1
            : '0' + (new Date(date).getMonth() + 1)) +
          '-' +
          (new Date(date).getDate() > 10
            ? new Date(date).getDate()
            : '0' + new Date(date).getDate())
        )
      } else {
        return (
          new Date(date).getFullYear().toString() +
          (new Date(date).getMonth() + 1 > 10
            ? new Date(date).getMonth() + 1
            : '0' + (new Date(date).getMonth() + 1)) +
          (new Date(date).getDate() > 10
            ? new Date(date).getDate()
            : '0' + new Date(date).getDate())
        )
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.wrap {
  min-height: 100vh;
}

.economy-container {
  box-sizing: border-box;
  border-radius: 18rpx 18rpx 0 0;
  background: url(@/static/img/home/<USER>
  background-size: 100% auto;

  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 20rpx;

    .icon-container {
      background-color: #fff;
      border-radius: 30rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20rpx;

      .iconfont {
        width: 60rpx;
        height: 60rpx;
      }
    }

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .indicator-card {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 0 25rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .left {
        display: flex;
        align-items: center;
      }

      .dot {
        width: 24rpx;
        height: 14rpx;
        margin-right: 16rpx;
      }

      .card-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .chart-subtitle {
      display: flex;
      justify-content: space-between;
      font-size: 24rpx;
      color: #999;
      margin-bottom: 10rpx;

      &.dual-axis {
        display: flex;
        justify-content: space-between;
      }

      .legend-container {
        display: flex;
        align-items: center;

        .legend-item {
          display: flex;
          align-items: center;
          margin-right: 20rpx;

          .legend-dot {
            width: 10rpx;
            height: 10rpx;
            border-radius: 50%;
            margin-right: 6rpx;
          }
        }
      }
    }

    .area-selector {
      display: flex;
      align-items: center;
      background-color: #fff;
      padding: 8rpx 16rpx;
      border-radius: 12rpx;
      font-size: 26rpx;
      color: #333;
      border: #aaaaaa solid 1rpx;

      .selector-icon {
        margin-left: 20rpx;
        .arrow-down {
          font-size: 20rpx;
          color: #666;
        }
      }
    }

    .chart-container {
      position: relative;

      .chart-center-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 2;

        .center-title {
          font-size: 28rpx;
          color: #666;
        }

        .center-value {
          font-size: 48rpx;
          font-weight: bold;
          color: #333;
        }
      }

      .pie-legend {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        margin-top: 10rpx;

        .legend-item {
          display: flex;
          align-items: center;
          margin-left: 30rpx;
          margin-bottom: 10rpx;

          .legend-color {
            width: 16rpx;
            height: 16rpx;
            border-radius: 50%;
            margin-right: 8rpx;
          }

          .legend-text {
            font-size: 26rpx;
            color: #666;
          }
        }
      }
    }
  }
}

// 区域选择弹窗样式
.area-popup {
  background-color: #fff;
  padding: 30rpx;

  .area-popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1px solid #eee;
    margin-bottom: 20rpx;

    text {
      font-size: 32rpx;
      font-weight: bold;
    }

    .close-btn {
      font-size: 40rpx;
      padding: 0 20rpx;
    }
  }

  .area-list {
    display: flex;
    flex-wrap: wrap;
    padding: 20rpx 0;

    .area-item {
      width: 33.33%;
      text-align: center;
      padding: 20rpx 0;
      font-size: 28rpx;

      &.active {
        color: #0098fa;
        font-weight: bold;
      }
    }
  }
}
</style>
