<template>
  <view class="comminuty-popup">
    <view class="search-wrap">
      <u-search
        v-model="key"
        placeholder="搜索您的社区"
        :show-action="false"
        clearabled
        @search="doSearch"
        @change="doSearch"
        @clear="clearSearch"
      ></u-search>
    </view>
    <view v-if="!showSearch">
      <view v-if="mySqData && mySqData.id" class="mine-sq-wrap">
        <view class="flex-between-c">
          <view class="wrap-title">我的社区</view>
          <view class="sel-btn">
            <u-button
              text="选择社区"
              type="primary"
              size="normal"
              @tap="selMysq"
            ></u-button>
          </view>
        </view>
        <view class="sq-name">{{ mySqData.name }}</view>
      </view>
      <view class="area-wrap">
        <view class="wrap-title">请选择地区</view>
        <view class="area-rows">
          <view class="area-row area-row-selected">
            <view>金华市</view>
          </view>
          <view
            class="area-row flex-between-c"
            :class="{
              'area-row-selected': mode > 1,
              'area-row-cur': mode === 1,
            }"
            @tap="areaRowTap"
          >
            <view class="text">{{ selAreaData.name || '请选择区县' }}</view>
            <view>
              <u-icon name="arrow-right" color="#999" size="18"></u-icon>
            </view>
          </view>
          <view
            class="area-row flex-between-c"
            :class="{
              'area-row-selected': mode > 2,
              'area-row-cur': mode === 2,
            }"
            @tap="streetRowTap"
          >
            <view class="text">{{ selStreetData.name || '请选择街道' }}</view>
            <view>
              <u-icon name="arrow-right" color="#999" size="18"></u-icon>
            </view>
          </view>
          <view
            class="area-row flex-between-c"
            :class="{
              'area-row-selected': mode > 3,
              'area-row-cur': mode === 3,
            }"
            @tap="communityRowTap"
          >
            <view class="text">
              {{ selCommunityData.name || '请选择社区' }}
            </view>
            <view>
              <u-icon name="arrow-right" color="#999" size="18"></u-icon>
            </view>
          </view>
        </view>
      </view>
      <view v-if="mode !== 4" class="options-wrap">
        <view class="wrap-title">
          <text v-if="mode === 1">请选择区县</text>
          <text v-if="mode === 2">请选择街道</text>
          <text v-if="mode === 3">请选择社区</text>
        </view>
        <view v-if="mode === 1" class="sel-options">
          <view
            v-for="item of curOptions"
            :key="item.name"
            class="sel-item"
            @tap="doSelArea(item)"
          >
            {{ item.name }}
          </view>
        </view>
        <view v-if="mode === 2" class="sel-options">
          <view
            v-for="item of curOptions"
            :key="item.name"
            class="sel-item"
            @tap="doSelStreet(item)"
          >
            {{ item.name }}
          </view>
        </view>
        <view v-if="mode === 3" class="sel-options">
          <view
            v-for="item of curOptions"
            :key="item.name"
            class="sel-item"
            @tap="doSelCommunity(item)"
          >
            {{ item.name }}
          </view>
        </view>
      </view>
    </view>
    <view v-if="showSearch" class="search-list">
      <view
        v-for="item of searchList"
        :key="item.name"
        class="search-item"
        @tap="searchItemClick(item)"
      >
        {{ `${item.qx}-${item.jd}-${item.name}` }}
      </view>
    </view>
  </view>
</template>

<script>
import { getSqTreeselect, getSqOptions } from '@/services/my/index.js'
export default {
  props: {
    mySqData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      key: '',
      sqTree: [],
      selAreaData: {}, // 选择的区县选项
      selStreetData: {}, // 选择的街道选项
      selCommunityData: {}, // 选择的社区选项
      curOptions: [], // 当前展示的下拉数据
      showSearch: false, // 是否显示搜索结果
      searchList: [], // 搜索结果，一维数组
    }
  },
  computed: {
    mode() {
      if (
        !this.selAreaData.name &&
        !this.selStreetData.name &&
        !this.selCommunityData.name
      ) {
        return 1
      }
      if (
        this.selAreaData.name &&
        !this.selStreetData.name &&
        !this.selCommunityData.name
      ) {
        return 2
      }
      if (
        this.selAreaData.name &&
        this.selStreetData.name &&
        !this.selCommunityData.name
      ) {
        return 3
      }
      if (
        this.selAreaData.name &&
        this.selStreetData.name &&
        this.selCommunityData.name
      ) {
        return 4
      }
      return 0
    },
  },
  mounted() {
    this.getTreeData()
  },
  methods: {
    searchItemClick(item) {
      this.$emit('confirm', {
        community: item,
      })
      this.reset()
    },
    doSearch() {
      console.log('doSearch')
      if (this.key) {
        this.getSearchData()
        this.showSearch = true
      } else {
        this.getTreeData()
        this.showSearch = false
      }
    },
    async getTreeData() {
      console.log('getTreeData')
      const d = await getSqTreeselect({ sq: this.key })
      console.log(d)
      if (d.code === 200) {
        this.sqTree = d.data
        this.curOptions = d.data
        console.log(this.curOptions, 'this.curOptions')
      }
    },
    async getSearchData() {
      const d = await getSqOptions({ sq: this.key })
      if (d.code === 200) {
        this.searchList = d.data
      }
    },
    doSelArea(item) {
      this.selAreaData = item
      this.curOptions = item.children
    },
    doSelStreet(item) {
      this.selStreetData = item
      this.curOptions = item.children
    },
    doSelCommunity(item) {
      console.log(item, 'doSelCommunity')
      this.selCommunityData = item
      this.curOptions = item.children
      this.$emit('confirm', {
        area: this.selAreaData,
        street: this.selStreetData,
        community: this.selCommunityData,
      })
      this.reset()
    },
    // 点击后重新选择区县
    areaRowTap() {
      this.selAreaData = {}
      this.selCommunityData = {}
      this.selStreetData = {}
      this.curOptions = this.sqTree
    },
    // 点击后重新选择街道
    streetRowTap() {
      if (!this.selAreaData.name) {
        uni.showToast({
          icon: 'none',
          title: '请先选择区县',
        })
        return
      }
      this.selCommunityData = {}
      this.selStreetData = {}
      this.curOptions = this.selAreaData.children
    },
    communityRowTap() {
      if (!this.selAreaData.name) {
        uni.showToast({
          icon: 'none',
          title: '请先选择区县',
        })
        return
      }
      if (!this.selStreetData.name) {
        uni.showToast({
          icon: 'none',
          title: '请先选择街道',
        })
        return
      }
    },
    selMysq() {
      this.$emit('confirm', {
        community: this.mySqData,
      })
      this.reset()
    },
    reset() {
      this.selAreaData = {}
      this.selStreetData = {}
      this.selCommunityData = {}
      this.key = ''
      this.showSearch = false
    },
    clearSearch() {
      this.key = ''
      this.showSearch = false
    },
  },
}
</script>

<style lang="scss" scoped>
.comminuty-popup {
  // #ifdef MP-WEIXIN
  height: 85vh;
  // #endif
  // #ifdef H5
  height: calc(100vh - 88rpx);
  // #endif
  overflow-y: auto;
  .search-wrap {
    padding: 80rpx 30rpx 30rpx;
  }
  .mine-sq-wrap {
    padding: 30rpx;
    border-bottom: 1px solid #eee;
    .sq-name {
      margin-top: 12rpx;
    }
  }
  .area-wrap {
    padding: 30rpx;
    border-bottom: 1px solid #eee;
    .area-rows {
      padding-left: 40rpx;
      padding-top: 24rpx;
      font-size: 28rpx;
      .area-row {
        position: relative;
        padding: 24rpx 0;
      }
      .area-row::before {
        position: absolute;
        left: -36rpx;
        top: 32rpx;
        content: '';
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        border: 4rpx solid #51a0f4;
        background: #fff;
      }
      .area-row:not(:last-child)::after {
        position: absolute;
        left: -28rpx;
        top: 50rpx;
        content: '';
        width: 4rpx;
        height: 70rpx;
        background: #51a0f4;
      }
      .area-row-selected {
      }
      .area-row-selected::before {
        width: 20rpx;
        height: 20rpx;
        background: #51a0f4;
        border: none;
      }
      .area-row-cur {
        .text {
          color: #51a0f4;
        }
      }
    }
  }
  .options-wrap {
    padding: 30rpx;
    .sel-options {
      margin: 30rpx 0 14rpx;
      .sel-item {
        margin: 30rpx 0;
      }
    }
  }
  .wrap-title {
    font-size: 36rpx;
    font-weight: bold;
  }
  .search-list {
    .search-item {
      padding: 40rpx 30rpx;
      border-bottom: 1px solid #eee;
    }
  }
}
</style>
