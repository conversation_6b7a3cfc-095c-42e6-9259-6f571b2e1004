<template>
  <view
    style="
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
    "
    @click="itemClick(item)"
  >
    <view
      :class="item.bgColor ? 'bgClass' : ''"
      :style="{
        'background-color': item.bgColor ? item.bgColor : 'transparent',
      }"
      style="
        width: 90%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;
      "
    >
      <i
        v-if="!item.imgUri && item.icon"
        :class="
          showBackground
            ? 'grid-icon-shadow icon iconfont ' + item.icon
            : 'icon iconfont ' + item.icon
        "
        :style="{
          color: showBackground ? '#ffffff' : item.color,
          'background-color': showBackground ? item.color : 'raba(0,0,0,0)',
          'border-radius': iconType + '%',
        }"
      ></i>
      <image
        v-if="item.imgUri"
        mode="aspectFit"
        :style="{ width: size + 'rpx', height: size + 'rpx' }"
        class="portrait-img"
        :src="item.imgUri"
      ></image>
      <image v-if="item.thumb" class="thumb" :src="item.thumb"></image>
      <view v-if="!(item.icon || item.thumb)" class="portrait">
        {{ item.text ? item.text.substr(1, 2) : '' }}
      </view>
      <rich-text
        class="title"
        :style="{ color: color }"
        :nodes="item.text"
      ></rich-text>
      <text v-if="item.note" class="note">{{ item.note }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CommonMenuItem',
  props: {
    item: {
      type: Object,
    },
    showBackground: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'square',
    },
    size: {
      default: 80,
    },
    color: {
      type: String,
      default: '#717070',
    },
  },

  data() {
    return {}
  },
  computed: {
    iconType: function () {
      switch (this.type) {
        case 'square':
          return 20
        case 'circle':
          return 50
        default:
          return 20
      }
    },
  },
  methods: {
    itemClick(item) {
      this.$emit('itemClick', item)
    },
  },
}
</script>

<style>
.iconfont {
  color: #ffffff;
  font-size: 50rpx;
}

.icon {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5rpx;
  width: 70rpx;
  height: 70rpx;
  -moz-border-radius: 20%;
  -webkit-border-radius: 20%;
  border-radius: 20%;
}

/* text {
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
	} */

.grid-icon-shadow {
  box-shadow: 0 15rpx 15rpx rgba(65, 136, 242, 0.55);
}

.title {
  width: 100%;
  text-align: center;
  font-size: 30rpx;
  margin-top: -12rpx;
  line-height: 30rpx;
  /* white-space: nowrap; */
  overflow: hidden;

  text-overflow: ellipsis;

  display: -webkit-box;

  -webkit-line-clamp: 2;

  -webkit-box-orient: vertical;

  color: #717070;
}

.note {
  font-size: 20rpx;
  line-height: 30rpx;
  border: 1px solid #878787;
  border-radius: 50rpx;
  padding: 0 20rpx;
}

.thumb {
  width: 70rpx;
  height: 70rpx;
}

.portrait {
  width: 80rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 50%;
  background-color: #007aff;
  color: #fff;
  text-align: center;
}

.portrait-img image {
  width: 100%;
  height: 100%;
}
.portrait-img {
  font-size: 30rpx;
  text-align: center;
  margin-top: 20rpx;
  margin-bottom: 30rpx;
}

::v-deep.portrait-img > div {
  background-position: center center !important;
  background-size: contain !important;
}

.bgClass {
  width: 130rpx;
  height: 130rpx;
  padding: 10rpx 0;
  color: #384ebf !important;
  border-radius: 8rpx;
}
</style>
