import { http } from '@/utils/http'

// banner图片
export const getBannerList = (data) => {
  const params = {
    url: '/screen/face/banner/list',
    method: 'GET',
    data,
  }
  return http(params)
}

// 消息通知
export const getindexMessageList = (data) => {
  const params = {
    url: '/screen/ajhIndexMessage/getIndexMessageList',
    method: 'GET',
    data,
  }
  return http(params)
}
// 首页分类菜单
export const getFlmenuList = (fl) => {
  const params = {
    url: '/screen/myApplication/list',
    method: 'GET',
    data:{
      pageNum:1,
      pageSize:10,
      fl
    }
  }
  return http(params)
}

// 首页本地资讯
export const getInfoList = (fl) => {
  const params = {
    // url: '/screen/consult/list',
    url: '/screen/consult/getZxlb',
    method: 'GET',
    data:{
      // pageNum:1,
      // pageSize:4
    }
  }
  return http(params)
}

// 首页爱生活
export const getlifeList = (data) => {
  const params = {
    // url: '/screen/consult/list',
    url: '/screen/poi/queryPoiByLabel',
    method: 'GET',
    data
  }
  return http(params)
}
// 首页模板渲染
export const getHomeAssembly = (data) => {
  const params = {
    // url: '/screen/consult/list',
    url: '/screen/homeAssembly/getHomeAssembly',
    method: 'GET',
    data
  }
  return http(params)
}
