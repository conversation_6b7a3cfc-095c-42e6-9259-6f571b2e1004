<script>
var first_route_arr = [
  'pages/shouye/index',
  'pages/message/index',
  // 'pages/community/index',
  // 'pages/life/h5map/index',
  'pages/finance/yfk/boss/index',
  'pages/myIndex/wode',
]
var ios_route_arr = [
  //少数页面在登录之后查看，防止ios侧滑会回到登录页bug
  'pages/finance/yfk/client/myprepaid',
  'pages/finance/yfk/client/prodDetails',
]
import { addHyd } from '@/services/ajhMdCount.js'
import { csdnLogin } from '@/services/csdnIndexApi'
export default {
  onLaunch: function (options) {
    console.log('app  onLaunch')

    uni.removeStorageSync('location')
    this.addRouterListen()
    const d = uni.getSystemInfoSync()
    uni.setStorageSync('systemInfo', d)
    const scene = options.scene
    uni.setStorageSync('scene', scene)
    // uni.getSystemInfo({
    //   success: (res) => {
    //     // alert(res.deviceId)
    //     uni.setStorageSync('systemInfo', res)
    //   },
    //   fail: (err) => {
    //     console.log(err)
    //   },
    // })
  },
  onShow: function () {
    this.hydInit()
    // #ifdef H5
    window.parent.postMessage(
      {
        cmd: 'H5load',
      },
      '*'
    )
    // #endif
    // process.env.NODE_ENV !== 'development'&&
  },
  created() {
    this.loginCsdn()
  },
  onHide: function () {
    this.hydExit()
  },
  globalData: {
    switchId: '',
  },
  watch: {
    '$store.state.token': {
      handler: function (newVal, oldVal) {
        if (newVal.length == 0) {
          // 退出登录
          this.hydExit(oldVal)
        } else {
          // 登录
          this.hydInit()
        }
      },
    },
  },
  methods: {
    loginCsdn() {
      csdnLogin(
        JSON.stringify({
          username: 'liyun',
          password: this.encode64('liyun@123'),
        })
      ).then((res) => {
        console.log('接口数据', res)
        if (res.code == '200') {
          uni.setStorageSync('csdnIndexApiToken', res.portToken)
          uni.setStorageSync('csdnIndexApiAuthorization', res.token)
          uni.setStorageSync('csdnIndexApiRole', res.userId)
        }
      })
    },
    encode64: function (val) {
      // base64加密开始
      var keyStr =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
      var output = ''
      var chr1,
        chr2,
        chr3 = ''
      var enc1,
        enc2,
        enc3,
        enc4 = ''
      var i = 0
      do {
        chr1 = val.charCodeAt(i++)
        chr2 = val.charCodeAt(i++)
        chr3 = val.charCodeAt(i++)
        enc1 = chr1 >> 2
        enc2 = ((chr1 & 3) << 4) | (chr2 >> 4)
        enc3 = ((chr2 & 15) << 2) | (chr3 >> 6)
        enc4 = chr3 & 63
        if (isNaN(chr2)) {
          enc3 = enc4 = 64
        } else if (isNaN(chr3)) {
          enc4 = 64
        }
        output =
          output +
          keyStr.charAt(enc1) +
          keyStr.charAt(enc2) +
          keyStr.charAt(enc3) +
          keyStr.charAt(enc4)
        chr1 = chr2 = chr3 = ''
        enc1 = enc2 = enc3 = enc4 = ''
      } while (i < val.length)
      return output
    },
    hydExit(token = '') {
      if (!this.$store.state.token && !token) return
      // 活跃度统计关闭
      // #ifdef MP-WEIXIN
      addHyd(
        {
          operateType: 2,
          type: 2,
        },
        token
      ).then((res) => {})
      // #endif
      // #ifdef H5
      addHyd(
        {
          operateType: 2,
          type: 1,
        },
        token
      ).then((res) => {})
      // #endif
    },
    hydInit() {
      if (!this.$store.state.token) return

      // 活跃度统计打开
      // #ifdef MP-WEIXIN
      addHyd({
        operateType: 1,
        type: 2,
      }).then((res) => {})
      // #endif
      // #ifdef H5
      addHyd({
        operateType: 1,
        type: 1,
      }).then((res) => {})
      // #endif
    },
    watchRouter() {
      setTimeout(() => {
        console.log('路由跳转')
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        let canExitType = ''
        if (!currentPage) return
        if (~first_route_arr.indexOf(currentPage.route)) {
          canExitType = 'canExit'
          console.log('canExit')
        } else {
          canExitType = 'canNotExit'
          console.log('canNotExit')
        }

        window.parent.postMessage(
          {
            cmd: 'route_change',
            currentPage: currentPage.route,
            canExitType: canExitType,
            iosRouteArr: ios_route_arr,
          },
          '*'
        )
      }, 1000) // 延迟去获得跳转后的页面路由
      // 	window.parent.postMessage({
      // 		cmd: 'route_change',
      // 		currentPage: currentPage.route
      // 	}, '*')
      // }, 1000) //延迟去获得跳转后的页面路由
    },
    disconnectSocket() {
      console.log('关闭连接')
      uni.closeSocket({
        code: 100,
        reason: '升级',
      })
      console.log('关闭连接')
    },
    sendMessage(obj) {
      uni.sendSocketMessage({
        data: JSON.stringify(obj),
        success: () => {
          console.log('发送成功', obj)
        },
        fail: function () {
          uni.showToast({
            icon: 'none',
            position: 'bottom',
            title: '数据发送失败',
          })
        },
      })
    },
    addRouterListen() {
      const that = this
      // #ifdef MP-WEIXIN
      uni.addInterceptor('navigateTo', {
        invoke(args) {
          const pages = getCurrentPages() // 获取加载的页面
          const newPages = pages.map((item) => item.route)

          const nowUrl = args.url
          const lastPage = newPages.length && newPages[newPages.length - 1]
          if (
            nowUrl.includes('/login/wxLogin') &&
            lastPage.includes('/login/wxLogin')
          ) {
            console.log('拦截多次跳转login')
            return false
          }
          return true
        },
        success(args) {
          console.log('app--拦截器success', args)
        },
        fail(err) {
          console.log('interceptor-fail', err)
        },
        complete(res) {
          console.log('interceptor-complete', res)
        },
      })
      uni.addInterceptor('redirectTo', {
        invoke(args) {
          const pages = getCurrentPages() // 获取加载的页面
          const newPages = pages.map((item) => item.route)

          const nowUrl = args.url
          const lastPage = newPages.length && newPages[newPages.length - 1]

          if (
            nowUrl.includes('/login/wxLogin') &&
            lastPage.includes('/login/wxLogin')
          ) {
            console.log('拦截多次跳转login')
            return false
          }
          return true
        },
        success(args) {
          console.log('app--拦截器success', args)
        },
        fail(err) {
          console.log('interceptor-fail', err)
        },
        complete(res) {
          console.log('interceptor-complete', res)
        },
      })

      // #endif
      // #ifdef H5
      // 监听路由跳转
      // 全局页面跳转拦截
      uni.addInterceptor('navigateTo', {
        invoke(args) {
          const pages = getCurrentPages() // 获取加载的页面
          const newPages = pages.map((item) => item.route)

          const nowUrl = args.url
          const lastPage = newPages.length && newPages[newPages.length - 1]

          if (
            nowUrl.includes('/login/index') &&
            lastPage.includes('/login/index')
          ) {
            console.log('拦截多次跳转login')

            return false
          }
          return true
        },
        success(args) {
          console.log('app--拦截器success', args)
          that.watchRouter()
        },
      })

      uni.addInterceptor('redirectTo', {
        // 监听关闭本页面跳转
        invoke(args) {
          const pages = getCurrentPages() // 获取加载的页面
          const newPages = pages.map((item) => item.route)

          const nowUrl = args.url
          const lastPage = newPages.length && newPages[newPages.length - 1]

          if (
            nowUrl.includes('/login/index') &&
            lastPage.includes('/login/index')
          ) {
            console.log('拦截多次跳转login')

            return false
          }
          return true
        },
        success(e) {
          that.watchRouter()
        },
      })
      uni.addInterceptor('switchTab', {
        // 监听tabBar跳转
        success(e) {
          that.watchRouter()
        },
      })
      uni.addInterceptor('reLaunch', {
        // 监听tabBar跳转
        success(e) {
          that.watchRouter()
        },
      })
      uni.addInterceptor('navigateBack', {
        // 监听返回
        success(e) {
          that.watchRouter()
        },
      })
      // 监听iframe父页面的消息
      window.addEventListener('message', (event) => {
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]

        // console.log(pages)
        if (event.data.cmd == 'navi_back') {
          if (~first_route_arr.indexOf(currentPage.route)) {
          } else {
            console.log('1111', pages.length)
            if (pages.length > 1) {
              uni.navigateBack({
                fail: (err) => {
                  uni.showToast({
                    icon: 'none',
                    title: '失败',
                  })
                },
              })
            } else {
              uni.switchTab({
                url: '/pages/shouye/index',
              })
            }
          }
        } else if (event.data.cmd == 'over') {
          if (uni.getStorageSync('meetingParam')) {
            const meetingParam = JSON.parse(uni.getStorageSync('meetingParam'))
            console.log('over子页面')
            const msgParam = {
              method: 'over',
              userList: [
                {
                  user_id: meetingParam.userList[0].user_id,
                },
              ],
              extra: {
                type: '取消',
                ...meetingParam,
              },
            }
            console.log(meetingParam.userList[0].user_id)
            uni.removeStorageSync('meetingParam')
            getApp().sendMessage(msgParam)
          }
        } else if (event.data.cmd == 'store') {
          console.log('lllllllllllllllll', event.data.value)
          that.$store.replaceState(JSON.parse(event.data.value)) // 初始化store
          // uni.setStorageSync('store', JSON.stringify(event.data.value))
          // store.replaceState(JSON.parse(uni.getStorageSync('store'))) // 初始化store
        }
      })
      // #endif
    },
  },
}
</script>

<style lang="scss">
@import 'uview-ui/index.scss';
@import './static/iconfont/iconfont.css';
@import './static/font.css';

/*每个页面公共css */
// @import '@/uni_modules/uview-ui/index.scss';
body {
  // font-family: 'SHSC-R';
  color: #222;
  // padding-top: 57rpx;
}

/* 原生组件模式下需要注意组件外部样式 */
custom-component {
  width: 100%;
  min-height: 100%;
  display: flex;
}

/* 原生组件模式下需要注意组件外部样式 */
m-input {
  width: 100%;
  min-height: 100%;
  display: flex;
}

.pad-iframe {
  width: 100%;
  height: 100%;
  border: unset;
}

// 其中var(--status-bar-height)为系统栏的高度
// margin-top: var(--status-bar-height);

/* #ifdef H5 */
uni-page-head .uni-page-head {
  height: calc(var(--safe-area-top) + 180rpx) !important;
  padding-top: 88rpx;
  // padding-top: var(--status-bar-height);
}

uni-page-head[uni-page-head-type='default'] ~ uni-page-wrapper {
  margin-top: calc(78rpx - env(safe-area-inset-top)) !important;
  // margin-top: -60rpx;
  // margin-top: env(safe-area-inset-top) !important;
  // margin-top: var(--status-bar-height);
}

uni-page-head[uni-page-head-type='default'] ~ uni-page-wrapper {
  height: calc(100% - 170rpx - env(safe-area-inset-top)) !important;
}

/* #endif */

.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

::v-deep .uni-tabbar__icon {
  // width: 26px !important;
  // height: 20px !important;
}
</style>
