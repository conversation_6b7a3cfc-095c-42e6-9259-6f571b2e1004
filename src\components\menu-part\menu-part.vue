<template>
  <view
    :style="[{backgroundColor: showBc ? '#fff' : 'transparent'},{margin:showBc ? '20rpx' :''},{borderRadius:showBc?'20rpx':'0'},{padding:showBc?'10rpx 10rpx':'0'}]">
    <view v-if="showTitle" class="nav-bar">
      <view class="flex" style="align-items: center;">
        <view class="title_before" v-if="showTitleLeft" :style="{'backgroundColor': menuConfig.bgcolor ? menuConfig.bgcolor:'#23a4ff'}">
        </view>
        <text class="nav-bar-title">{{menuConfig.title}}</text>

      </view>
      <view v-if="showCollsape && menuConfig.menus.length>=limit+1">
        <text v-if="!isShowAll" style="font-size: 20rpx; color: #0285e4;" class="iconfont icon-jia"
          @click="showAll(menuConfig)">更多</text>
        <text v-if="isShowAll" style="font-size: 20rpx; color: #0285e4;" class="iconfont icon-jia"
          @click="closeAll">收起</text>
      </view>
      <view v-else-if="showMore">
        <!-- <view v-else-if="showMore && menuConfig.menus.length>=limit+1"> -->
        <text style="font-size: 20rpx; color: #0285e4;" class="iconfont icon-jia" @click="onClickMore">更多</text>
      </view>

    </view>
    <view class="menu-panel menu-panel-grid" v-if="menuConfig.size=='small'">
      <view class="containe_list" :class="item.disable?'beGray':''" v-for="(item,index) in menu_array" :key="index"
        :style="{'width':`cacl(100% /${menuConfig.column})`}" @click="onClickMenuItem(item,index)">
        <common-menu-item :item="item"></common-menu-item>
      </view>

    </view>
    <view class="menu-panel  scroll-pannel-grid" v-else-if="menuConfig.type=='scroll'">

      <u-scroll-list>
        <view v-for="(item,index) in menu_array" :key="index" class="menu-gtid-item" :class="item.disable?'beGray':''"
          style="min-width:39%;max-width:39%" @click="onClickMenuItem(item,index)">
          <view class="menu-item" :class="menu_array.length>1&&(index+1)%2?'menu-item-left':'menu-item-right'"
            :style="{'backgroundImage' : 'url('+item.bgUri+')'}">
            <!-- :style="'backgroundImage:url(@'+item.bgUri+')'" -->

            <text :style="{'color':item.color,'whiteSpace':'nowrap'}" class="small_title">{{item.text}}</text>
            <text style="line-height: 30rpx; font-size: 25rpx; color: #428FFC;">了解详情</text>
            <!-- <image v-if="item.bgUri" mode="aspectFit" class="portrait-img" :src="item.bgUri">
        		</image> -->
          </view>
          <!-- <slot v-if="showSlot" name="slot-item" :item="item" :index="index"></slot> -->
        </view>
      </u-scroll-list>

    </view>
    <!-- <view style=" height: 1rpx; margin: 5rpx 10rpx ;background-color: #f2f2f2;"></view> -->
    <view class="clomn1-panel" v-else-if="column==1">
      <view class="column1-item flex-between-c" v-for="(item,index) in menu_array" :key="index">
        <view class="flex-c-c">
          <image :src="item.imgUri" class="column1-item-img" mode=""></image>
          <view class="">
            <view class="small_title1">{{item.text}}</view>
            <text class="subtitle">{{item.subText}}</text>
          </view>
        </view>
        <view class="">
          <view class="btn-ck" @click="onClickMenuItem(item,index)">
            查看
          </view>
          <!-- <button @click="onClickMenuItem(item,index)">查看</button> -->
        </view>
      </view>
    </view>
    <view v-else-if="limit==3&&column==2" class="menu-panel menu-panel-grid">
      <view class="menu-gtid-item">

        <view class="menu-item menu-item-high menu-item-left limit3-left-item"
          :class="menu_array[0].disable?'beGray':''" @click="onClickMenuItem(menu_array[0],0)"
          :style="{'backgroundImage' : 'url('+menu_array[0].bgUri+')' }">
          <view class="menu-item-high-text flex-c">
            <text>{{menu_array[0].text}}</text>
          <image :src="$getStaticImg('img/shouye/new/r1.png')" style="margin-left: 10rpx;width:33rpx;height: 25rpx;display: inline-block;vertical-align: middle;" mode=""></image>
          </view>
          <text style="line-height: 30rpx; font-size: 25rpx;">{{menu_array[0].subText}}</text>
        </view>
      </view>
      <view class="menu-gtid-item">
        <view class="menu-item menu-item-low menu-item-right limit3-right-item"
          :class="menu_array[1].disable?'beGray':''" @click="onClickMenuItem(menu_array[1],1)"
          :style="{'backgroundImage' : 'url('+menu_array[1].bgUri+')' }">
          <!-- <text class="menu-item-low-text">{{menu_array[1].text}}</text> -->
          <view class="menu-item-low-text flex-c">
            <text>{{menu_array[1].text}}</text>
          <image :src="$getStaticImg('img/shouye/new/r2.png')" style="width:50rpx;height: 40rpx;display: inline-block;vertical-align: middle;" mode=""></image>
          </view>
          <text style="line-height: 30rpx; font-size: 25rpx;">{{menu_array[1].subText}}</text>
        </view>
        <view class="menu-item menu-item-low menu-item-right limit3-right-item"
          :class="menu_array[2].disable?'beGray':''" @click="onClickMenuItem(menu_array[2],2)"
          :style="{'backgroundImage' : 'url('+menu_array[2].bgUri+')' }">
          <view class="menu-item-low-text flex-c">
            <text>{{menu_array[2].text}}</text>
          <image :src="$getStaticImg('img/shouye/new/r3.png')" style="width:50rpx;height: 40rpx;display: inline-block;vertical-align: middle;" mode=""></image>
          </view>
          <text style="line-height: 30rpx; font-size: 25rpx;">{{menu_array[2].subText}}</text>
        </view>
      </view>
    </view>
    <view v-else class="menu-panel menu-panel-grid">
      <view v-for="(item,index) in menu_array" :key="index" class="menu-gtid-item" :class="item.disable?'beGray':''"
        :style="{'min-width':(100/column)+'%','max-width':(100/column)+'%'}" @click="onClickMenuItem(item,index)">
        <view v-if="!showSlot" class="menu-item"
          :class="menu_array.length>1&&(index+1)%2?'menu-item-left':'menu-item-right'"
          :style="{'backgroundImage' : 'url('+item.bgUri+')','height':height}">

          <!-- :style="'backgroundImage:url(@'+item.bgUri+')'" -->
          <text :style="{'color':item.color}" class="small_title">{{item.text}}</text>
          <text style="line-height: 30rpx; font-size: 25rpx;color: #767676;margin-top: 20rpx;">{{item.subText}}</text>
          <!-- <image v-if="item.bgUri" mode="aspectFit" class="portrait-img" :src="item.bgUri">
					</image> -->
        </view>
        <slot v-if="showSlot" name="slot-item" :item="item" :index="index"></slot>
      </view>
    </view>

  </view>
</template>

<script>
  export default {
    name: "menu-part",
    props: {
      menuConfig: {
        type: Object
      },
      btn: {
        type: Boolean,
        default: false
      },
      showTitle: {
        type: Boolean,
        default: true
      },
      showMore: {
        type: Boolean,
        default: true
      },
      showCollsape: {
        type: Boolean,
        default: false
      },
      limit: {
        type: Number,
        default: 8
      },
      showTitleLeft:{
        type: Boolean,
        default: true
      },
      showBc: {
        type: Boolean,
        default: false
      },
      column: {
        type: Number,
        default: 2
      },
      height: {
        type: String,
        default: '180rpx'
      },
      showSlot: {
        type: Boolean,
        default: false
      },
    },
    watch: {
      menuConfig: {
        handler(newVal, oldVal) {
          // console.log('watch')
          this.initMenu()
        },
        deep: true
      }
    },
    data() {
      return {
        menu_array: [],
        isShowAll: false,
        edit: false
      };
    },
    created() {
      this.initMenu()
    },
    mounted() {},
    methods: {
      initMenu() {
        this.menu_array = this.menuConfig.menus.filter((item, index) => {
          return index < this.limit
        })
        // console.log(this.menu_array);

      },
      showAll(item) {

        // this.isShowAll = true
        // this.menu_array = JSON.parse(JSON.stringify(this.menuConfig.menus))

        // this.$forceUpdate()
        getApp().globalData.switchId = item.id
        	// uni.switchTab({
        	// 		 url:'/pages/affairListMain/index'

        	// 			})


      },
      closeAll() {
        this.isShowAll = false
        this.initMenu()
      },
      onClickMore() {

        this.$emit("onClickMore", this.menuConfig)
      },
      onClickMenuItem(item, index) {

        // console.log(index)
        if (item.disabled) {
          uni.showToast({
            icon: 'none',
            title: '暂未开放'
          })
          return
        }
        this.$emit("onClickMenuItem", item)
      },
      editClick() {
        this.edit = !this.edit
        this.$emit('editClick', edit)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .nav-bar {
    margin: 10rpx 10rpx;
    font-size: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .nav-bar-title {
    font-size: 33rpx;
    /* font-weight: bold; */
    color: #3c3b3c;
    padding-left: 10rpx;
    /* border-left: 5px solid #23a4ff; */

  }

  .menu-panel {}

  .menu-panel-grid {

    display: flex;
    justify-content: flex-start;
    align-items: center;
    /* flex-direction: column; */
    /* padding: 0 20rpx; */
    flex-wrap: wrap;
    align-items: flex-start;

  }

  .scroll-pannel-grid {
    padding-top: 30rpx;

    .menu-item {
      // width: 237rpx !important;
      height: 126rpx !important;

    }

    .small_title {
      margin: 0;
      color: #222222 !important;
      font-size: 32rpx;
    }
  }

  .menu-gtid-item {
    flex: 1;
  }

  .menu-item {
    position: relative;
    box-sizing: border-box;
    /* background-color: #00c4ff; */
    box-shadow: 5rpx 5rpx 10rpx 0rpx rgba(96, 96, 96, 0.29);
    border-radius: 7rpx;
    /* height: 180rpx; */
    margin: 0 8rpx 16rpx;
    padding: 32rpx 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    /* background-image: url("@/static/img/shouye/jjtj-bc.png"); */
    background-size: 100% 100%;
    background-position: 0 0;
  }

  .menu-item-left {
    /* margin-right: 10rpx; */
    /* box-shadow: unset; */
  }

  .limit3-left-item,
  .limit3-right-item {
    box-shadow: unset !important;
  }

  .menu-item-right {
    /* margin-left: 10rpx; */
    /* box-shadow: unset; */
  }

  .menu-item-low {
    height: 89rpx;
    color: #875151;
  }

  .menu-item-high {
    height: 194rpx;
    color: #875151;
  }

  .menu-item-low-text,
  .menu-item-high-text {
    position: absolute;
    bottom: 8rpx;
    right: 10rpx;
    font-size: 30rpx;
    // font-family: MicrosoftYaHei;
    font-family: Roboto, Roboto;
  }

  .portrait-img {
    position: absolute;
    top: -20rpx;
    left: -20rpx;
    width: 130%;
    height: 100%;
    font-size: 30rpx;
    text-align: center;
    margin-top: 20rpx;
    display: block;
    z-index: -999;
  }

  .containe_list {
    display: flex;
    justify-content: space-evenly;
    width: 25%;
  }

  .title_before {
    width: 10rpx;
    height: 25rpx;
    background-color: #23a4ff;
    border-radius: 5rpx
  }

  .small_title {
    margin-left: 50rpx;
    position: relative;
    top: -7rpx;
  }

  button {
    color: #2682fa;
    font-size: 30rpx;
    font-weight: 400;
    width: 120rpx;
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 40rpx;
    margin: 0;
  }

  .flex {
    display: flex;
  }

  .scroll-list {
    @include flex(column);

    &__goods-item {
      margin-right: 20px;
      width: 300rpx;

      &__image {
        width: 60px;
        height: 60px;
        border-radius: 4px;
      }

      &__text {
        color: #f56c6c;
        text-align: center;
        font-size: 12px;
        margin-top: 5px;
      }
    }

    &__show-more {
      background-color: #fff0f0;
      border-radius: 3px;
      padding: 3px 6px;
      @include flex(column);
      align-items: center;

      &__text {
        font-size: 12px;
        width: 12px;
        color: #f56c6c;
        line-height: 16px;
      }
    }
  }

  .column1-item {
    display: flex;
    border-top: 2rpx solid #F5F6F8;
    height: 132rpx;
    padding: 0 30rpx;
    button{
      width: 180rpx;
      height: 64rpx;
      background: #E4EDFF;
      border-radius: 32px 32px 32px 32px;
      opacity: 1;
      color: #000;
      border: unset;
      position: unset;

    }
    uni-button:after{
      width: 0;
      height: 0;
    }
    .column1-item-img {
      width: 80rpx;
      height: 80rpx;
      margin-right: 20rpx;


    }
    .small_title1 {
      font-size: 32rpx;

    }

    .subtitle {
      line-height: 20rpx;
      font-size: 25rpx;
      color: #999999;
    }
  }
  .btn-ck{
    width: 180rpx;
        height: 64rpx;
        background: #E4EDFF;
        border-radius:60rpx;
        opacity: 1;
        text-align: center;
        line-height: 64rpx;

  }
</style>
