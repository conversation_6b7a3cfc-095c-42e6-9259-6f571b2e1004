<template>
  <view>
    <common-upload v-model="images" :max-count="3"></common-upload>
    <avatar-upload v-model="avatar"></avatar-upload>
  </view>
</template>

<script>
import CommonUpload from '@/components/common-upload/common-upload.vue'
import AvatarUpload from '@/components/avatar-upload/avatar-upload.vue'
export default {
  name: 'Demo',
  components: {
    CommonUpload,
    AvatarUpload,
  },
  data() {
    return {
      images: [
        { url: 'https://cdn.uviewui.com/uview/swiper/1.jpg' },
        // { url: 'https://cdn.uviewui.com/uview/swiper/2.jpg' },
        // { url: 'https://cdn.uviewui.com/uview/swiper/3.jpg' },
      ],
      avatar: {},
    }
  },
  watch: {
    images: {
      handler(newVal, oldVal) {
        console.log(newVal, oldVal, 'images变了')
      },
      immediate: true,
      deep: true,
    },
    avatar: {
      handler(newVal, oldVal) {
        console.log(newVal, oldVal, 'avatar变了')
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {},
}
</script>

<style lang="scss" scoped></style>
