import { csdnRequest } from '@/utils/http'

// 城市大脑指标接口
export const getCsdnInterface = (url, data) => {
  const params = {
    url: '/api/?indexid=/' + url,
    method: 'get',
    headers: {
      portToken: uni.getStorageSync("csdnIndexApiToken"),
      ptid: 'PT0001',
    },
    data
  }
  return csdnRequest(params)
}


//大脑登录接口
export function csdnLogin(data) {
  const params = {
    url: '/adm-api/auth/login',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    }
  }
  return csdnRequest(params)
}

//鉴权接口
export function csdnAuth(data) {
  return csdnRequest({
    url: '/typeq/api/auth/creditAuth',
    method: 'post',
    data
  })
}
