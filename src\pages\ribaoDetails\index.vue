<template>
  <view class="shouye-index">
    <view class="titleCon">金华城市大脑</view>
    <view class="main-wrap">
      <view class="banner">
        <image
          style="width: 100%; height: 100%"
          :src="banner2"
          mode="widthFix"
        />
        <view class="btn btn1" @click="changeReportType"></view>
      </view>

      <!-- Tab导航 -->
      <view
        class="tab-nav"
        :class="{ 'fixed-tab': isTabFixed }"
        :style="isTabFixed ? 'top: ' + statusBarHeight + 'px' : ''"
        id="tab-nav"
      >
        <view
          v-for="(item, index) in tabs"
          :key="index"
          class="tab-item"
          :class="{ active: currentTab === index }"
          @click="switchTab(index)"
        >
          {{ item.name }}
        </view>
      </view>

      <!-- 占位元素，防止吸顶时内容跳动 -->
      <view v-if="isTabFixed" class="tab-placeholder"></view>
      <sskb :currentTab="currentTab"></sskb>
    </view>
    <view class="page-gray"></view>
  </view>
</template>

<script>
import sskb from './sskb.vue'

export default {
  components: {
    sskb,
  },
  data() {
    return {
      reportType: 0,
      banner2: require('@/static/img/home/<USER>'),
      tabs: [
        { name: '经济', id: 'jingji' },
        { name: '民生', id: 'minsheng' },
        { name: '环境', id: 'huanjing' },
        { name: '平安', id: 'pingan' },
      ],
      currentTab: 0,
      isTabFixed: false,
      tabNavTop: 0,
      statusBarHeight: 0,
      tabHeight: 0,
    }
  },
  onLoad() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
  },
  onShow() {},
  onPageScroll(e) {
    // 页面滚动时触发
    if (e.scrollTop >= this.tabNavTop && !this.isTabFixed) {
      this.isTabFixed = true
    } else if (e.scrollTop < this.tabNavTop && this.isTabFixed) {
      this.isTabFixed = false
    }
  },
  methods: {
    changeReportType() {
      uni.navigateTo({
        url: '/pages/shouye/index',
      })
    },
    switchTab(index) {
      this.currentTab = index
    },
    // 获取tab导航栏的位置信息
    getTabPosition() {
      const query = uni.createSelectorQuery().in(this)
      query
        .select('#tab-nav')
        .boundingClientRect((data) => {
          if (data) {
            this.tabNavTop = data.top
            this.tabHeight = data.height
          }
        })
        .exec()
    },
  },
  mounted() {
    // 在页面挂载后获取tab导航栏的位置
    setTimeout(() => {
      this.getTabPosition()
    }, 200)
  },
  onPullDownRefresh() {},
  onReachBottom() {},
}
</script>

<style lang="scss" scoped>
.shouye-index {
  position: relative;
  .titleCon {
    width: 100%;
    padding: 20px 0;
    box-sizing: border-box;
    background-color: #fff;
    text-align: center;
  }
  .main-wrap {
    .banner {
      padding: 30rpx 25rpx 0 25rpx;
      box-sizing: border-box;
      width: 100%;
      position: relative;
      // height: 200rpx;
      image {
        border-radius: 12rpx;
      }
      .btn {
        width: 126rpx;
        height: 36rpx;
        z-index: 1;
        position: absolute;
        top: 50rpx;
        right: 26rpx;
      }
      .btn1 {
        background: url('@/static/img/home/<USER>');
        background-size: 100% 100%;
      }
      .btn2 {
        background: url('@/static/img/home/<USER>');
        background-size: 100% 100%;
      }
    }

    .tab-nav {
      display: flex;
      justify-content: space-around;
      padding: 0 25rpx 20rpx 25rpx;
      background-color: #f9fafb;
      z-index: 10;
      width: 100%;
      box-sizing: border-box;

      &.fixed-tab {
        position: fixed;
        left: 0;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
      }

      .tab-item {
        padding: 20rpx 0;
        position: relative;
        font-size: 36rpx;
        color: #666;

        &.active {
          color: #000;
          font-weight: bold;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60rpx;
            height: 6rpx;
            background-color: #2979ff;
            border-radius: 4rpx;
          }
        }
      }
    }

    // 占位元素，高度与tab导航栏相同
    .tab-placeholder {
      height: 80rpx; // 这个高度应该与tab-nav的实际高度一致
    }

    .tab-content {
      // padding: 20rpx 0rpx;
      min-height: 300rpx;
    }
  }
}
</style>
