{
  "easycom": {
    // "^u-(.*)": "uview-ui/components/u-$1/u-$1.vue",
    "^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
  },
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/shouye/index",
      "style": {
        "navigationBarTitleText": "首页",
        "transparentTitle": "always",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/ribaoDetails/index",
      "style": {
        "navigationBarTitleText": "实时看板",
        "transparentTitle": "always",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/demo/index",
      "style": {
        "navigationBarTitleText": "demo"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "城市大脑日报",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "condition": {
    //模式配置，仅开发期间生效
    "current": 0, //当前激活的模式(list 的索引项)
    "list": [
      {
        "name": "", //模式名称
        "path": "pages/my/login/index", //启动页面，必选
        "query": "" //启动参数，在页面的onLoad函数里面得到
      }
    ]
  }
}
