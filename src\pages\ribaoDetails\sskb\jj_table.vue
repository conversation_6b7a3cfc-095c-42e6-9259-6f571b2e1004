<template>
  <view class="tableWrap">
    <view v-for="(item, i) in tableList" :key="i">
      <view class="tableItem" v-if="item.list[0].value1">
        <view class="flex-baseline" style="margin-bottom: 20rpx">
          <view class="itemHead">{{ item.indexName }}</view>
          <view class="dept">责任单位：{{ item.list[0].dept || '-' }}</view>
        </view>
        <view class="table" v-for="(x, j) in item.list" :key="j">
          <view class="tableHead" v-if="item.list.length > 1">
            {{ x.indexName }}
          </view>
          <view class="line">
            <view class="td" style="flex: 0.6"></view>
            <view class="td">
              <view>最近一周</view>
              <view class="time">{{ weekTime }}</view>
            </view>
            <view class="td">
              <view>月度累计</view>
              <view class="time">{{ monthTime }}</view>
            </view>
            <view class="td">
              <view>本年累计</view>
              <view class="time">{{ yearTime }}</view>
            </view>
          </view>
          <view class="line">
            <view class="td" style="flex: 0.6">绝对值</view>
            <view class="td">{{ x.value1 }}{{ x.unit }}</view>
            <view class="td">{{ x.value2 }}{{ x.unit }}</view>
            <view class="td">{{ x.value3 }}{{ x.unit }}</view>
          </view>
          <view class="line">
            <view class="td" style="flex: 0.6">同比</view>
            <view
              class="td"
              :class="parseFloat(x.tb1) > 0 ? 'text_red' : 'text_green'"
            >
              {{ x.tb1 }}
            </view>
            <view
              class="td"
              :class="parseFloat(x.tb2) > 0 ? 'text_red' : 'text_green'"
            >
              {{ x.tb2 }}
            </view>
            <view
              class="td"
              :class="parseFloat(x.tb3) > 0 ? 'text_red' : 'text_green'"
            >
              {{ x.tb3 }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import indexData from '@/pages/ribaoDetails/sskb/indexData.json'
import { getCsdnInterface } from '@/services/csdnIndexApi/index.js'

export default {
  data() {
    return {
      tableList: [],
      weekTime: '',
      monthTime: '',
      yearTime: '',
    }
  },
  mounted() {
    this.generateTimeFields()
    this.init()
  },
  methods: {
    // 生成时间字段的函数
    generateTimeFields() {
      const now = new Date()
      const month = now.getMonth() + 1
      const date = now.getDate()

      // 获取当前周的开始和结束日期
      const dayOfWeek = now.getDay() // 0是周日，1是周一
      const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek // 计算到周一的偏移
      const monday = new Date(now)
      monday.setDate(date + mondayOffset)
      const sunday = new Date(monday)
      sunday.setDate(monday.getDate() + 6)

      // 格式化日期为 "月日" 格式
      const formatDate = (date) => {
        const month = date.getMonth() + 1
        const day = date.getDate()
        return `${month}月${day}日`
      }

      // 生成三个时间字段
      this.weekTime = `${formatDate(monday)}-${formatDate(sunday)}`
      this.monthTime = `${month}月1日-${formatDate(now)}`
      this.yearTime = `至${formatDate(now)}`
    },

    init() {
      getCsdnInterface('ldrb_jjzhyxybz').then((res) => {
        let lastDate = ''
        if (res.responsecode == 200) {
          lastDate = res.data[res.total - 1].tjjzrq
          this.$emit('lastDate', lastDate)
          getCsdnInterface('ldrb_jjzhyxybz', { tjjzrq: lastDate }).then(
            (res) => {
              let data = res.data.map((item) => {
                return {
                  indexName: item.zbmc,
                  value1: item.zjyzjdz ? item.zjyzjdz : '',
                  tb1: item.zjyztb ? item.zjyztb + '%' : '',
                  value2: item.ydljjdz ? item.ydljjdz : '',
                  tb2: item.ydljtb ? item.ydljtb + '%' : '',
                  value3: item.bnljjdz ? item.bnljjdz : '',
                  tb3: item.bnljtb ? item.bnljtb + '%' : '',
                  dept: item.zrdw,
                  unit: item.zbdw ? item.zbdw : '',
                }
              })
              this.formatData(data)
            }
          )
        }
      })
      // this.formatData(indexData)
    },
    formatData(data) {
      let arr = []
      let j = 0 //记录上一个可能的父指标
      data.forEach((item, i) => {
        if (item.indexName.indexOf('其中') == -1) {
          //是独立指标或者父指标
          let obj = {
            indexName: item.indexName,
            list: [item],
          }
          arr.push(obj)
          j = i
        } else {
          arr[j].list.push(item)
        }
      })
      this.tableList = arr
    },
  },
}
</script>

<style lang="scss" scoped>
.tableWrap {
  .tableItem {
    background-color: #edf7ff;
    margin-bottom: 20rpx;
    border-radius: 12rpx;
    padding: 30rpx 16rpx;
    box-sizing: border-box;
    .table {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 28rpx;
      color: #363a44;
      line-height: 42rpx;
      background-color: #fff;
      padding: 20rpx;
      box-sizing: border-box;
      .tableHead {
        padding: 10rpx 0;
        box-sizing: border-box;
        border: solid 1rpx #d8dee4;
        text-align: center;
      }
    }
    .dept {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 24rpx;
      color: #363a44;
      line-height: 36rpx;
      white-space: nowrap;
      margin-left: 20rpx;
    }
    .line {
      display: flex;
    }
    .td {
      flex: 1;
      border: solid 1rpx #d8dee4;
      text-align: center;
      padding: 10rpx 0;
      box-sizing: border-box;
    }
    .time {
      font-size: 20rpx;
      white-space: nowrap;
    }
    .text_red {
      color: #d9001b;
    }
    .text_green {
      color: #4b795b;
    }
  }
}
.flex-baseline {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
}
</style>
