<template>
  <view class="action-select" @tap="toggleShow">
    <u-input
      v-model="name"
      :disabled="disabled"
      readonly
      :placeholder="placeholder"
      border="none"
    >
      <template slot="suffix">
        <view v-show="!show">
          <u-icon name="arrow-right"></u-icon>
        </view>
        <view v-show="show">
          <u-icon name="arrow-down"></u-icon>
        </view>
      </template>
    </u-input>
    <u-action-sheet
      :show="show"
      :actions="actions"
      :close-on-click-overlay="false"
      :title="actionTitle"
      :safe-area-inset-bottom="true"
      @close="show = false"
      @select="doSelect"
    ></u-action-sheet>
  </view>
</template>

<script>
export default {
  props: {
    value: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
    actionTitle: {
      type: String,
      default: '请选择',
    },
    actions: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    idkey: {
      type: String,
      default: 'id',
    },
    namekey: {
      type: String,
      default: 'name',
    },
  },
  data() {
    return {
      name: '',
      selectItem: {},
      show: false,
    }
  },
  watch: {
    value: {
      handler(newVal, oldVal) {
        console.log(newVal, 'value变了')
        this.updateName()
        // const f = this.actions.find((item) => item[this.idkey] === newVal)
        // if (f) {
        //   this.name = f[this.namekey]
        // } else {
        //   this.name = ''
        // }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    updateName() {
      const f = this.actions.find((item) => item[this.idkey] === this.value)
      if (f) {
        this.name = f[this.namekey]
      } else {
        this.name = ''
      }
    },
    toggleShow() {
      console.log('toggleShow')
      if (!this.disabled) {
        this.show = !this.show
      }
    },
    doSelect(e) {
      console.log(e, 'doSelect')
      this.name = e[this.namekey]
      this.selectItem = e
      this.$emit('input', e[this.idkey])
      this.$emit('select', this.selectItem)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .u-action-sheet__item-wrap {
  max-height: 75vh;
  overflow-y: auto;
}
</style>
