<template>
  <view>
    <view
      class="indicator-item purple-bg"
      v-for="(item, i) in list"
      :key="i"
      @click="showDetail(item)"
    >
      <view class="purple-header">
        <view class="title-value">
          <text class="title">{{ item.title }}</text>
          <text class="value">
            {{ item.value || ' - ' }}
            <text class="unit">{{ item.unit }}</text>
          </text>
        </view>
      </view>
      <view class="update-time-row">
        <view class="flex-c">
          <image src="@/static/img/home/<USER>" class="update-icon" />
          <text>更新时间：{{ item.updateTime || ' - ' }}</text>
        </view>
        <view
          class="item-growth"
          style="margin-bottom: 0"
          v-if="item.growthLabel"
        >
          {{ item.growthLabel }}
          <text class="growth-value" :class="item.growth > 0 ? 'up' : 'down'">
            {{
              item.growth == ''
                ? '-'
                : parseFloat(item.growth) !== 0
                ? parseFloat(item.growth).toFixed(2) + '%'
                : item.growth
            }}
          </text>
          <image
            v-if="parseFloat(item.growth) !== 0 && item.growth !== ''"
            :src="item.growth > 0 ? upIcon : downIcon"
            style="width: 14rpx; height: 20rpx; margin-left: 8rpx"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      upIcon: require('@/static/img/home/<USER>'),
      downIcon: require('@/static/img/home/<USER>'),
    }
  },
  methods: {
    showDetail(item) {
      this.$emit('showDetail', item)
    },
  },
}
</script>

<style lang="scss" scoped>
.indicators-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .indicator-item {
    width: calc((100% - 22rpx - 80rpx) / 2);
    height: 340rpx;
    text-align: center;

    margin-right: 22rpx;
    &:nth-child(2n + 2) {
      margin-right: 0;
    }
  }
}

.indicator-item {
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  text-align: center;
}
.blue-bg {
  background: url(@/static/img/home/<USER>
  background-size: 100% 100%;
}

.purple-bg {
  background: url(@/static/img/home/<USER>
  background-size: 100% 100%;
  padding: 30rpx;

  .purple-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title-value {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-size: 40rpx;
        font-weight: bold;
        color: #333;
        margin-right: 20rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .value {
        font-size: 48rpx;
        font-weight: bold;
        color: #ff6b18;
        white-space: nowrap;

        .unit {
          font-size: 30rpx;
          font-weight: normal;
          margin-left: 8rpx;
          white-space: nowrap;
        }
      }
    }
  }
}

.item-title {
  height: 92rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 700;
  font-size: 40rpx;
  color: #333333;
  line-height: 46rpx;
  text-align: center;
}

.item-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b18;
  margin-bottom: 10rpx;

  .unit {
    font-size: 30rpx;
    font-weight: normal;
    margin-left: 8rpx;
  }
}

.item-growth {
  font-size: 32rpx;
  color: #8e98a4;
  margin-bottom: 30rpx;

  .growth-value {
    margin-left: 8rpx;
    &.up {
      color: #cd2020;
    }
    &.down {
      color: #1aa269;
    }
  }
}

.update-time {
  font-size: 32rpx;
  color: #8e98a4;

  .time {
    color: #586779;
    font-weight: 500;
    margin-top: 5rpx;
  }
}

.update-time-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 32rpx;
  color: #586779;
  margin-top: 20rpx;

  .update-icon {
    width: 30rpx;
    height: 30rpx;
    margin-right: 10rpx;
  }
}
</style>
