<template>
  <view class="activity_box" @click="goCamp">
    <view class="activity_bkg_box">
      <view
        class="activity_bkg"
        :style="{ backgroundImage: 'url(' + campInfo.headImg + ')' }"
      ></view>
      <view class="activity_bkg_black"></view>
    </view>
    <view class="activity_top">
      <view class="activity_info">
        <image
          :src="campInfo.headImg"
          style=""
          class="header_pic"
          mode="aspectFill"
        ></image>
        <view class="info_box">
          <view class="name">{{ campInfo.campName }}</view>
          <view class="remark">{{ campInfo.introduce }}</view>
        </view>
      </view>
      <view
        v-if="type == 1"
        class="activity_btn"
        @click.stop.prevent="goYingdi"
      >
        <view class="btn_txt">发现活动</view>
        <image
          :src="$getStaticImg('img/shouye/new/activity_btn_icon.png')"
          style=""
          class="btn_icon"
          mode=""
        ></image>
      </view>
      <view
        v-if="!campInfo.isCreator && type == 2 && !campInfo.joined"
        class="activity_btn1"
        :style="{
          background:
            'url(' +
            $getStaticImg('img/activity/campsite/jiaru_icon.png') +
            ') no-repeat',
          backgroundSize: 'cover',
        }"
        @click.stop.prevent="goJoin"
      >
        <view class="btn_jia">+</view>
        <view class="btn_txt">加入</view>
      </view>
      <view
        v-if="!campInfo.isCreator && type == 2 && campInfo.joined"
        class="activity_btn2"
        @click.stop.prevent="goOut"
      >
        <view class="btn_txt">退出</view>
      </view>
    </view>
    <view class="activity_bottom">
      <view
        v-if="campInfo.list && campInfo.list.length > 0"
        class="activity_list"
      >
        <view
          v-for="(item, aindex) in campInfo.list"
          :key="aindex"
          class="activity_item"
          @click.stop.prevent="goActivity(item)"
        >
          <image
            :src="item.activityImg.split(',')[0]"
            style=""
            class="activity_left_img"
            mode="aspectFill"
          ></image>
          <view class="activity_right">
            <view class="name ws1">{{ item.activityName }}</view>
            <view class="date_box">
              <image
                :src="$getStaticImg('img/shouye/new/activity_date.png')"
                style=""
                class="date_icon"
                mode=""
              ></image>
              <view class="date">
                {{ item.beginTime ? item.beginTime.slice(5, 16) : '暂无' }}
              </view>
            </view>
            <view class="addr_box">
              <image
                :src="$getStaticImg('img/shouye/new/activity_addr.png')"
                style=""
                class="addr_icon"
                mode=""
              ></image>
              <view class="addr ws1">{{ item.address }}</view>
            </view>
            <view
              v-if="item.userAvatar && item.userAvatar.length > 0"
              class="person_box"
            >
              <view class="person_list">
                <image
                  v-for="(pic, pix) in item.userAvatar"
                  :key="pix"
                  :src="
                    pic
                      ? pic
                      : $getStaticImg(
                          'img/activity/campsite/camp_head_icon.png'
                        )
                  "
                  style=""
                  class="person_item"
                  mode="aspectFill"
                ></image>
              </view>
              <view class="person_count">{{ item.activityNum }}人想去</view>
            </view>
            <view v-else class="person_box">
              <view class="person_count">马上去报名</view>
            </view>
          </view>
        </view>
      </view>
      <view v-else class="no_data">
        <image
          :src="$getStaticImg('img/shouye/new/no_activity_icon.png')"
          style=""
          class="no_data_icon"
          mode=""
        ></image>
        <view class="no_data_txt">快来发起第一个活动吧~</view>
      </view>
    </view>
  </view>
</template>

<script>
import { followCamp, disableCamp } from '@/services/activity/campsite.js'
export default {
  props: {
    type: {
      type: Number,
      default: 1,
    },
    campInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {}
  },
  watch: {},
  methods: {
    goYingdi() {
      uni.navigateTo({
        url: '/pages/activity/index',
      })
    },
    // 跳转集结地详情
    goCamp() {
      uni.navigateTo({
        url:
          '/pages/activity/campsite/campsiteDetails?campId=' +
          this.campInfo.campId,
      })
    },
    // 跳转活动详情
    goActivity(item) {
      uni.navigateTo({
        url:
          '/pages/activity/activityDetail/index?extra=' +
          encodeURIComponent(JSON.stringify(item.activityId)),
      })
    },
    // 加入
    goJoin() {
      followCamp({ campId: this.campInfo.campId }).then((res) => {
        if (res.code == 200) {
          uni.$u.toast('加入成功')
          this.$emit('statusChange')
        }
      })
    },
    // 退出
    goOut() {
      disableCamp({ campId: this.campInfo.campId }).then((res) => {
        if (res.code == 200) {
          uni.$u.toast('退出成功')
          this.$emit('statusChange')
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.activity_box {
  width: 715rpx;
  height: 472rpx;
  border-radius: 29rpx 29rpx 29rpx 29rpx;
  padding: 32rpx 0 0 30rpx;
  box-sizing: border-box;
  position: relative;
  .activity_bkg_box {
    width: 715rpx;
    height: 472rpx;
    border-radius: 29rpx 29rpx 29rpx 29rpx;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 0;
    overflow: hidden;
    .activity_bkg {
      position: absolute;
      z-index: 1;
      width: 715rpx;
      height: 472rpx;
      border-radius: 29rpx 29rpx 29rpx 29rpx;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: 50% 50%;
      filter: blur(3px) opacity(0.8) grayscale(0.2);
    }
    .activity_bkg_black {
      position: absolute;
      z-index: 2;
      width: 715rpx;
      height: 472rpx;
      border-radius: 29rpx 29rpx 29rpx 29rpx;
      background: rgba(0, 0, 0, 0.3);
      opacity: 0.5;
    }
  }

  .activity_top {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    .activity_info {
      display: flex;
      align-content: center;
      align-items: center;
      .header_pic {
        width: 105rpx;
        height: 105rpx;
        border-radius: 19rpx 19rpx 19rpx 19rpx;
        border: 1rpx solid #ffffff;
        .pic {
          width: 105rpx;
          height: 105rpx;
        }
      }
      .info_box {
        margin-left: 20rpx;
        .name {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 700;
          font-size: 34rpx;
          color: #ffffff;
        }
        .remark {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 27rpx;
          color: #ffffff;
          margin-top: 12rpx;
        }
      }
    }
    .activity_btn {
      width: 172rpx;
      height: 46rpx;
      background: linear-gradient(90deg, #3affd4 0%, #3af8ff 100%);
      border-radius: 29rpx 29rpx 29rpx 29rpx;
      margin-right: 28rpx;
      display: flex;
      align-content: center;
      align-items: center;
      .btn_txt {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 700;
        font-size: 25rpx;
        color: #000000;
        margin-left: 18rpx;
      }
      .btn_icon {
        width: 27rpx;
        height: 27rpx;
        margin-left: 10rpx;
        .pic {
          width: 27rpx;
          height: 27rpx;
        }
      }
    }
    .activity_btn1 {
      width: 126rpx;
      height: 50rpx;
      margin-right: 28rpx;
      display: flex;
      align-content: center;
      align-items: center;
      justify-content: center;
      .btn_jia {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 700;
        font-size: 28rpx;
        color: #1d2129;
      }
      .btn_txt {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 700;
        font-size: 28rpx;
        color: #1d2129;
        margin-left: 8rpx;
      }
    }
    .activity_btn2 {
      width: 130rpx;
      height: 50rpx;
      margin-right: 28rpx;
      background: rgba(255, 255, 255, 0.5966);
      border: 1rpx solid rgba(151, 151, 151, 0.401);
      border-radius: 24rpx;
      .btn_txt {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 700;
        font-size: 28rpx;
        color: #1d2129;
        line-height: 50rpx;
        text-align: center;
      }
    }
  }
  .activity_bottom {
    position: relative;
    z-index: 1;
    margin-top: 28rpx;
    .activity_list {
      display: flex;
      overflow-x: auto;
      overflow-y: hidden;
      .activity_item {
        display: flex;
        margin-right: 28rpx;
        width: 487rpx;
        height: 277rpx;
        background: #ffffff;
        border-radius: 19rpx 19rpx 19rpx 19rpx;
        .activity_left_img {
          width: 219rpx;
          height: 277rpx;
          border-radius: 19rpx;
        }
        .activity_right {
          flex: 1;
          width: 267rpx;
          .name {
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 500;
            font-size: 34rpx;
            color: #1d2129;
            line-height: 41rpx;
            margin-left: 24rpx;
            margin-top: 18rpx;
          }
          .date_box {
            display: flex;
            align-content: center;
            align-items: center;
            margin-left: 18rpx;
            margin-top: 56rpx;
            .date_icon {
              width: 25rpx;
              height: 25rpx;
            }
            .date {
              font-family: Source Han Sans, Source Han Sans;
              font-weight: 400;
              font-size: 28rpx;
              color: #86909c;
              margin-left: 10rpx;
            }
          }
          .addr_box {
            display: flex;
            align-content: center;
            align-items: center;
            margin-left: 14rpx;
            margin-top: 12rpx;
            .addr_icon {
              width: 20rpx;
              height: 25rpx;
            }
            .addr {
              font-family: Source Han Sans, Source Han Sans;
              font-weight: 400;
              font-size: 28rpx;
              color: #86909c;
              margin-left: 10rpx;
              flex: 1;
            }
          }
          .person_box {
            display: flex;
            align-content: center;
            align-items: center;
            margin-top: 18rpx;
            margin-left: 8rpx;
            .person_list {
              display: flex;
              align-content: center;
              align-items: center;
              .person_item {
                width: 40rpx;
                height: 40rpx;
                border-radius: 50%;
                position: relative;
                left: -10rpx;
                &:first-child {
                  left: 0;
                }
                &:nth-child(3) {
                  left: -20rpx;
                }
              }
            }
            .person_count {
              font-family: Source Han Sans, Source Han Sans;
              font-weight: 400;
              font-size: 24rpx;
              color: #afb6bf;
              margin-left: 20rpx;
            }
          }
        }
      }
    }
  }
  .no_data {
    text-align: center;
    padding-top: 60rpx;
    .no_data_icon {
      width: 100rpx;
      height: 85rpx;
    }
    .no_data_txt {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 32rpx;
      color: #ffffff;
      line-height: 27rpx;
      text-align: center;
      margin-top: 36rpx;
    }
  }
}
</style>
