import Vue from 'vue'
import App from './App'
import { http } from './utils/http.js'
import { tenminHttp } from './utils/tenminHttp.js'
// import uView from '@/uni_modules/uview-ui'
import uView from 'uview-ui'
import './index.scss'
// import uView from 'uview-ui'
import store from './store'
import {
  getStaticImg,
  getStaticTenminImg,
  getURLParameters,
} from './utils/index.js'
import { getPosition, getLifeAuth } from './js/utils'
import share from './js/share'
import { ajhMdCount } from '@/services/ajhMdCount.js'
import AmapVue from '@amap/amap-vue'
import dd from 'gdt-jsapi';
Vue.prototype.dd = dd
Vue.use(uView)
Vue.mixin(share)

// 注册全局组件
//挂载到全局，让所有页面都能接收
Vue.prototype.$http = http //挂载到Vue的原型上
Vue.prototype.$tenminHttp = tenminHttp //挂载到Vue的原型上
Vue.prototype.$getStaticImg = getStaticImg
Vue.prototype.$getURLParameters = getURLParameters
Vue.prototype.$getStaticTenminImg = getStaticTenminImg
Vue.prototype.$getPosition = getPosition
Vue.prototype.$getLifeAuth = getLifeAuth
Vue.prototype.$ajhMdCount = ajhMdCount

Vue.config.productionTip = false
Vue.prototype.$getRect = function (selector, all) {
  return new Promise((resolve) => {
    uni
      .createSelectorQuery()
      .in(this)
      [all ? 'selectAll' : 'select'](selector)
      .boundingClientRect((rect) => {
        if (all && Array.isArray(rect) && rect.length) {
          resolve(rect)
        }
        if (!all && rect) {
          resolve(rect)
        }
      })
      .exec()
  })
}

Vue.prototype.$store = store
App.mpType = 'app'
AmapVue.config.version = '2.0' // 默认2.0，这里可以不修改
AmapVue.config.key = 'df956d6b0e5f77e7e41398cdaf541e5d'
AmapVue.config.plugins = [
  'AMap.ToolBar',
  'AMap.MoveAnimation',
  // 在此配置你需要预加载的插件，如果不配置，在使用到的时候会自动异步加载
]
Vue.use(AmapVue)

const app = new Vue({
  ...App,
})
app.$mount()
