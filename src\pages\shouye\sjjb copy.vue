<template>
  <view>
    <!-- Tab内容区域 -->
    <view
      class="tab-content"
      style="padding-bottom: 40rpx"
      ref="pdfContent"
      id="pdfContent"
    >
      <view class="indicator-card">
        <view class="card-header flex-b">
          <view class="flex-c">
            <image src="@/static/img/home/<USER>" class="dot" />
            <text class="card-title">
              {{ currentTab == 0 ? '日' : currentTab == 1 ? '周' : '月' }}报概览
            </text>
          </view>
          <view class="exportBtn" @click="toImage" v-if="!getImging">
            导出简报
          </view>
        </view>
        <view class="card">
          <div class="listCon">
            <!-- <view>经济、民生、平安、环境：</view> -->
            <view class="listItem flex-c" v-for="(item, i) in list" :key="i">
              <view style="margin-right: 20rpx">{{ item.name }}:</view>
              <view class="icon" :style="{ background: item.color }"></view>
              <view>{{ item.text }}</view>
            </view>
          </div>
        </view>
      </view>

      <view class="indicator-card" v-if="currentTab == 1">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">AI摘要</text>
        </view>
        <view class="card">
          <image
            v-if="!showAi"
            src="@/static/img/home/<USER>"
            class="aiBtn"
            @click="generateReport"
          />
          <view v-else>
            <view class="aiThinking">
              <view class="thinkHeader flex-b" @click="handleThinking">
                <view class="flex-c">
                  <image src="@/static/img/home/<USER>" class="star" />
                  <text class="card-title">
                    {{ thinking ? 'AI思考中' : 'AI回答完成' }}
                  </text>
                </view>
                <image
                  src="@/static/img/home/<USER>"
                  class="icon"
                  :class="showThinking ? 'icon_rotated' : ''"
                />
              </view>
              <u-parse
                ref="aiThinking"
                :content="displayThinking"
                class="thinkContent"
                :class="showThinking ? 'thinkContent_all' : ''"
              ></u-parse>
            </view>
            <view class="aiReport">
              <u-parse
                ref="aiReport"
                :content="displayReport"
                class="parseClass"
              ></u-parse>
            </view>
          </view>
        </view>
      </view>

      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">
            每{{ currentTab == 0 ? '日' : currentTab == 1 ? '周' : '月' }}摘要
          </text>
        </view>
        <!-- 经济 -->
        <view class="card" v-if="textList1.length > 0">
          <view class="header" :style="{ backgroundColor: '#0098fa1a' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">经济</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList1" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
          <!-- <view style="width: 100%; height: 330rpx">
            <LEchart ref="lineChart" style="width: 100%; height: 100%" />
          </view> -->
        </view>
        <!-- 民生 -->
        <view class="card" v-if="textList2.length > 0">
          <view class="header" :style="{ backgroundColor: '#F276291A' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">民生</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList2" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
        </view>
        <!-- 环境 -->
        <view class="card" v-if="textList3.length > 0">
          <view class="header" :style="{ backgroundColor: '#0CD9B51A' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">环境</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList3" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
        </view>
        <!-- 平安 -->
        <view class="card" v-if="textList4.length > 0">
          <view class="header" :style="{ backgroundColor: '#FDCC001A' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">平安</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList4" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
        </view>
      </view>

      <!-- <image
        v-if="posterUrl"
        :src="posterUrl"
        style="width: 100%"
        mode="widthFix"
      /> -->
    </view>
  </view>
</template>

<script>
import html2canvas from 'html2canvas'
import { jsPDF } from 'jspdf'
import * as echarts from '@/pages/shouye/components/lime-echart/static/echarts.min.js'
import LEchart from '@/pages/shouye/components/lime-echart/components/l-echart/l-echart.vue'
import { getCsdnInterface } from '@/services/csdnIndexApi/index.js'

export default {
  components: {
    LEchart,
  },
  props: {
    currentTab: {
      type: Number,
      default: 0,
    },
  },
  watch: {
    currentTab: {
      handler(newVal, oldVal) {
        console.log(newVal)
        //暂时
        if (this.currentTab == 0) {
          this.textList1 = [
            '1、今日新增市场主体数XX家dot_blue，同比增长XX%，注销市场主体数XX家dot_green，同比降低XX%。截至今日，当年累计新增市场主体数XX家dot_blue，同比增长XX%，当年累计注销市场主体数XX家dot_yellow，同比降低XX%。',
            '2、今日全社会用电量为XXX万亿瓦时dot_blue，同比增长XX%，表现优秀。',
            '3、金华全域游客量为XXXX人dot_green，同比增长XX%。其中东阳市商业客流量增长较为明显，游客量为XXX人dot_blue，同比增长XX%。',
            '4、金华高速车流量为XXXX辆dot_green，其中，义乌市进口总量最多，为XXXX辆dot_red，东阳市出口总量最多，为XXXX辆dot_green。武义县同比增长最多，为XXXX辆dot_blue，同比增长XX%，永康市同比增长最少，为XXX辆dot_blue，同比减少XX%',
          ]
          this.textList2 = [
            '1、今日出生XX人，死亡XX人dot_blue。截至今日，当年累计出生XX人dot_green，同比增长XX%，当年累计死亡XX人dot_yellow，同比降低XX%。',
            '2、金华全域政务服务办件量为XXXX件dot_blue，同比增长XX%，主动评价件数为XXXXX件，主动评价率为XX%，差评数为XXX件dot_red，占比XX%，同比减少XX%。',
            '3、重要民生商品价格有X个商品有价格变动，有X种商品价格变动较大，其中，XXX商品，涨幅XXX元/克dot_blue；XXX商品，涨幅XXX元/克dot_blue。',
          ]
          this.textList3 = [
            '1、今日AQI指数表现大幅度异常，其中PM2.5为102ug/m³dot_yellow，同比上升XX%，PM10为10ug/m³dot_green，同比上升XX%',
          ]
          this.textList4 = [
            '1、警情数为XXX起，其中110警情XX起dot_blue，同比增加XX%；119警情XX起dot_blue，同比增加XX%；122警情XX起dot_blue，同比增加XX%；综合接警XX起，同比增加XX%；非警务XX起dot_blue，同比增加XX%。其中，婺城区警情数表现优秀，警情数为XX起dot_blue，同比降低XX%；义乌市警情数表现异常，警情数为XX起dot_red，同比增加',
            '2、今日电信诈骗小幅度异常，被诈骗次数XX次dot_blue，同比增长XX%；诈骗金额XXX万元dot_yellow，同比增长XX%。其中，婺城区小幅度异常，被诈骗次数XX次dot_blue，同比增长XX%。',
            '3、今日信访批次XX批dot_blue，同比增长XX%；信访人次XX人dot_blue，同比增长XX%。',
          ]
        } else if (this.currentTab == 1) {
          this.textList1 = [
            '1、本周工业用电量为XXX亿千瓦时dot_blue，同比增长XXX%，表现优秀；截至当前，当月工业用电量为XXX亿千瓦时dot_blue，同比增长XXX%，表现正常；当年工业用电量为XXX亿千瓦时dot_blue，同比增长XXX%，表现正常。',
            '2、本周进出口为XXX亿元dot_blue，同比增长XXX%，表现优秀；出口为XXX亿元dot_yellow，同比增长XXX%，表现小幅度异常；其中，结关出口为XXX亿元dot_blue，同比降低XXX%，表现小幅度异常；其中，对美结关出口为XXX亿元dot_red，同比降低XXX%，表现大幅度异常；进口为XXX亿元dot_blue，同比增长XXX%，表现正常。',
            '3、本周新房销售量为XX平方米dot_blue，同比增长XX%，二手房销售量为XX平方米dot_blue，同比增长XX%。其中，金东区新房销量增长最快，为XX平方米dot_blue，同比增长XX%；磐安县二手房销量增长最快dot_blue，为XX平方米，同比增长XX%。',
            '4、固定资产投资为XXX亿元dot_blue，同比增长XXX%，其中民间投资占固定资产投资比重XXX%，同比增长%。',
          ]
          this.textList2 = []
          this.textList3 = []
          this.textList4 = []
        } else if (this.currentTab == 2) {
          this.textList1 = [
            '1、本月新增市场主体数XX家dot_blue，同比增长XX%，注销市场主体数XX家dot_green，同比降低XX%。截至本月，当年累计新增市场主体数XX家dot_blue，同比增长XX%，当年累计注销市场主体数XX家dot_yellow，同比降低XX%。',
            '2、本月全社会用电量为XXX万亿瓦时dot_blue，同比增长XX%，表现优秀。',
            '3、金华全域游客量为XXXX人dot_green，同比增长XX%。其中东阳市商业客流量增长较为明显，游客量为XXX人dot_blue，同比增长XX%。',
            '4、金华高速车流量为XXXX辆dot_green，其中，义乌市进口总量最多，为XXXX辆dot_red，东阳市出口总量最多，为XXXX辆dot_green。武义县同比增长最多，为XXXX辆dot_blue，同比增长XX%，永康市同比增长最少，为XXX辆dot_blue，同比减少XX%',
          ]
          this.textList2 = [
            '1、本月出生XX人，死亡XX人dot_blue。截至本月，当年累计出生XX人dot_green，同比增长XX%，当年累计死亡XX人dot_yellow，同比降低XX%。',
            '2、金华全域政务服务办件量为XXXX件dot_blue，同比增长XX%，主动评价件数为XXXXX件，主动评价率为XX%，差评数为XXX件dot_red，占比XX%，同比减少XX%。',
            '3、重要民生商品价格有X个商品有价格变动，有X种商品价格变动较大，其中，XXX商品，涨幅XXX元/克dot_blue；XXX商品，涨幅XXX元/克dot_blue。',
          ]
          this.textList3 = [
            '1、本月AQI指数表现大幅度异常，其中PM2.5为102ug/m³dot_yellow，同比上升XX%，PM10为10ug/m³dot_green，同比上升XX%',
          ]
          this.textList4 = [
            '1、警情数为XXX起，其中110警情XX起dot_blue，同比增加XX%；119警情XX起dot_blue，同比增加XX%；122警情XX起dot_blue，同比增加XX%；综合接警XX起，同比增加XX%；非警务XX起dot_blue，同比增加XX%。其中，婺城区警情数表现优秀，警情数为XX起dot_blue，同比降低XX%；义乌市警情数表现异常，警情数为XX起dot_red，同比增加',
            '2、本月电信诈骗小幅度异常，被诈骗次数XX次dot_blue，同比增长XX%；诈骗金额XXX万元dot_yellow，同比增长XX%。其中，婺城区小幅度异常，被诈骗次数XX次dot_blue，同比增长XX%。',
            '3、本月信访批次XX批dot_blue，同比增长XX%；信访人次XX人dot_blue，同比增长XX%。',
          ]
        }
        this.textAddDot(this.textList1)
        this.textAddDot(this.textList2)
        this.textAddDot(this.textList3)
        this.textAddDot(this.textList4)
      },
      immediate: true,
    },
  },
  data() {
    return {
      list: [
        { name: '经济', color: '#0098FA', text: '表现优秀（指数≥90）' },
        { name: '民生', color: '#0CD9B5', text: '正常、良好（90>指数≥80）' },
        { name: '平安', color: '#FDCC00', text: '小幅度异常（80>指数≥60）' },
        { name: '环境', color: '#F27629', text: '大幅度异常（60>指数≥0）' },
      ],
      textList1: [],
      textList2: [],
      textList3: [],
      textList4: [],
      lineChartData: {
        xAxis: [
          '2023-11',
          '2023-12',
          '2024-01',
          '2024-02',
          '2024-03',
          '2024-04',
        ],
        values: [50, 60, 40, 80, 50, 70],
      },
      lineChartOption: {
        grid: {
          top: 10,
          right: 0,
          bottom: 10,
          left: 0,
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: '',
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
          },
        },
        yAxis: {
          type: 'value',
          name: '',
          min: 0,
          max: 100,
          interval: 20,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
            },
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
          },
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}%',
        },
        series: [
          {
            data: '',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2,
              color: '#2979ff',
            },
            itemStyle: {
              color: '#2979ff',
            },
          },
        ],
      },
      //大模型
      showAi: false, //是否启用大模型ai
      showThinking: false, //ai思考部分是否展示
      thinking: false, //是否在流式输出中
      thinkingContent: '', //接口返回的answer
      paragraph:
        '<think>\n嗯，我需要比较这两份经济周报的数据，看看有哪些变化和关联。首先注意到统计截止日期不同，一份是2025-05-23，另一份是2025-05-16，也就是相隔一周的数据。接下来，我要逐一对比各个指标在这两周的变化情况，并找出可能存在的关联。\n\n首先看工业高压用电量，从5月16日的7.10亿千万时增长到5月23日的7.20，同比增幅也有所提升，说明工业生产活动有所增加。增值税开票销售收入也呈现增长，本周绝对值从371.26亿上升到477.09亿，可能是工业生产增加带动了销售收入的上升。不过，义乌市场日均外商人数略有下降，从3930人减少到3850人，可能对当地市场消费产生影响，但同比增速仍在正增长，说明整体趋势还是向上的。\n\n义乌机场旅客吞吐量在第二周有所下降，从72550人减少到71047人，但月度累计同比增速从14.13%下降到10.60%。这可能与航班调整或季节性因素有关，旅客减少可能间接影响了酒店入住率。金华酒店入住率在这周的数据从436,443降低到435,156，虽然降幅不大，但同比降幅扩大，从-9.00%到-16.10%，说明酒店业可能面临压力。\n\n再看消费方面，银联线下消费增长显著，从116.30亿元增加到142.50亿元，同比增速从49.30%提升到78.70%，这可能与促销活动或节假日消费有关。但机动车新上牌数量有所下降，从6,262辆减至5,431辆，可能与购车需求暂时饱和或政策变化有关。\n\n快递业务量持续增长，从38,040万件增加到41,400万件，同比增速也有所提升，反映出电商和物流行业的活跃，这也可能与义乌包裹发送量的变化相关。不过，义乌市邮件互换局发送包裹数量在第二周绝对值下降，但同比降幅从-38.59%减缓到-34.20%，可能显示出口包裹量虽减少但趋势有所改善。\n\n生产安全方面，生产安全事故数在第二周降至0起，同比降幅扩大，而亡人事故也有所减少，显示安全管理措施可能有所加强。交通安全亡人数从3人减少到1人，治安警情数也有所下降，可能反映出公共安全管理的成效。\n\n环保指标中，PM2.5日均浓度显著下降，从29.6微克/立方米降至16.3，同比变化从增长12.50%转为大幅下降3560%，可能得益于环保措施或天气条件改善。\n\n需要关注的是，工业用电和增值税销售的增长与市场外商人数和酒店入住率的下滑形成对比，可能说明工业生产的提升并未完全转化为服务业的同步增长。消费数据的强劲可能与线下促销有关，但机动车销售下降可能影响相关产业链。快递业务量的增长与义乌包裹发送的波动可能反映出口结构的变化。安全指标的改善显示管理有效，但需持续关注潜在风险。\n</think>\n\n从5月16日至23日的数据对比显示，工业生产与消费呈现差异化走势。工业高压用电量周度绝对值环比增长1.4%，带动增值税开票销售收入周度值增长28.5%，但义乌市场外商日均人数下降2.0%，可能对商贸活跃度形成制约。消费市场表现强劲，银联线下消费周度值激增22.5%，但机动车新上牌数量下降13.3%，显示耐用品消费动力不足。物流领域出现结构性变化，快递业务量周度增长8.8%，而义乌邮件互换局包裹发送量下降3.5%，反映商品流通渠道的转变。中欧班列运量保持双位数增长，周度标箱发送量环比降9.4%但仍保持14.3%同比增速，显示国际物流韧性。安全生产领域成效显著，周度生产安全事故实现零突破，亡人数降幅达100%。环境治理取得突破性进展，PM2.5日均浓度骤降44.9%，创下3560个百分点的同比改善幅度。房地产市场分化明显，商品房成交面积周度暴增2095%，而二手房交易量下降6.7%，显示政策刺激效果集中在新房市场。这些数据折射出经济结构调整中的动能转换特征，新旧业态交替对经济运行产生复合影响。\n############\n<think>\n嗯，用户需要根据金华市的历史天气数据来分析其对全社会用电量趋势的影响，并生成一段大约200字的摘要。首先，我需要仔细看看提供的天气数据，找出其中的变化规律，然后联系用电量的可能波动。\n\n首先看天气情况，从5月25日到6月3日，金华市的天气变化比较多端，有阴、雨、多云、晴等多种天气。温度方面，这段时间的温度范围大致在17℃到29℃之间，整体气温逐渐上升，但中间有几天温度有所下降，比如5月28日中雨转小雨时，温度降到17℃～21℃，而到6月3日又回升到19℃～26℃。这种温度波动可能会影响空调的使用，进而影响用电量。\n\n然后看降雨情况，5月27日到6月2日期间多次出现大雨、中雨和小雨，尤其是5月31日和6月1日有中雨和大雨。连续的阴雨天气可能导致气温较为凉爽，减少空调的制冷需求，从而降低用电量。而晴天和多云天气，比如5月30日和5月26日，温度较高，可能会增加空调的使用，导致用电量上升。\n\n另外，风力方面，大部分时间风力较小，只有个别日子有3-4级的风。风力较小可能对用电量影响不大，但如果是大风天气可能会影响电力输送或导致临时停电，但这里的情况可能不显著。\n\n需要综合考虑温度、降雨和天气类型对用电的影响。例如，高温天气会增加制冷需求，提升用电量；阴雨天气降低温度，减少制冷需求，可能用电量下降。同时，持续的降雨可能影响工业生产或户外活动，间接影响用电量。此外，不同日期的天气变化可能导致用电量的波动，比如从雨天转晴时，用电量可能突然增加。\n\n然后要组织语言，先概述天气情况，再分点说明温度、降雨等因素如何影响用电量，最后总结整体趋势。要注意逻辑连贯，用数据支撑观点，比如具体温度范围和降雨日期。还要确保大约200字，不要太长或太短。\n\n可能的结构：开头总述天气情况，中间分温度、降水等因素分析，最后总结对用电量趋势的影响。注意使用具体日期和天气数据作为例子，让分析更有说服力。\n</think>\n\n根据金华市2025年5月25日至6月3日的天气数据分析，用电量趋势呈现明显的天气关联性。在5月30日（29℃晴转多云）和5月26日（27℃阴转晴）等气温较高的晴天，制冷需求可能推高商业和居民用电；而5月28日（21℃中雨）及6月2日（24℃大雨）等低温阴雨天气，用电负荷相对较低。值得注意的是，5月31日-6月1日持续降雨期间（中雨转大雨，21-28℃），虽然温度适中，但工业生产可能受降水影响降低产能，导致工业用电需求减少。此外，昼夜温差保持在8-10℃之间，夜间低温减少空调使用时间，客观上缓和了用电峰值。整体来看，晴热天气与用电高峰存在正相关性，而持续性降水则对用电量产生抑制作用，天气因素通过温度调节和生产经营活动双重路径影响着电力消费波动。',
      paragraphStreaming: '', //这是真的流式
      //模拟流式
      displayThinking: '', // 新增显示用文本
      displayReport: '', // 新增显示用文本
      streamingInterval: null, // 定时器
      //导出
      posterUrl: '',
      getImging: false,
    }
  },
  filters: {
    addDot(value) {
      return
    },
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.$refs.lineChart.init(echarts, (e) => {
    //     this.lineChartOption.xAxis.data = this.lineChartData.xAxis
    //     this.lineChartOption.series[0].data = this.lineChartData.values
    //     e.setOption(this.lineChartOption)
    //   })
    // })
  },
  methods: {
    async getAiReport() {
      //流式输出
      const response = await fetch(
        'http://*************:9438/v1/chat-messages',
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer app-YvnosvwSKfaZIxaGZizhFfv5`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            inputs: {},
            query: 'What are the specs of the iPhone 13 Pro Max?',
            response_mode: 'streaming',
            conversation_id: '',
            user: 'abc-123',
          }),
        }
      )

      const reader = response.body.getReader()
      let decoder = new TextDecoder('utf-8')

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        let chunk = decoder.decode(value, { stream: true })
        let dataList = chunk.split('\n\n')
        dataList.forEach((item) => {
          if (item) {
            if (item.slice(0, 5) == 'data:' && item.indexOf('answer') !== -1) {
              let obj = JSON.parse(item.replace('data:', ''))
              if (obj.answer && obj.answer.length > 0) {
                this.paragraphStreaming += obj.answer
              }
            }
          }
        })
      }
    },
    async getAiReport2() {
      //每日更新的数据库,模拟流式
      getCsdnInterface('ldrb_zysc').then((res) => {
        if (res.responsecode == 200) {
          this.paragraph = res.data[0].content
          // console.log(this.paragraph);
          // this.paragraph = this.paragraph.replaceAll('<think', '<p')
          this.paragraph = this.paragraph.replaceAll('############', '')
          const arr1 = this.paragraph.split('<think>')
          const arr11 = arr1[1].split('</think>') //获取周报数据比对arr11[1]
          const arr12 = arr1[2].split('</think>') //获取用电量和气温变化关系分析arr12[1]
          this.thinkingContent =
            '<p>' + arr11[0] + '</p>\n<p>' + arr12[0] + '</p>'
          this.paragraph = '<p>' + arr11[1] + '</p>\n<p>' + arr12[1] + '</p>'
          // console.log(this.paragraph)
          this.startStreaming(this.thinkingContent, 1)
        }
      })
    },
    startStreaming(fullText, type) {
      const chars = fullText.split('')
      let currentIndex = 0
      if (type == 1) {
        // this.showThinking = true
        this.streamingInterval = setInterval(() => {
          if (currentIndex < chars.length) {
            this.displayThinking += chars[currentIndex]
            currentIndex++

            scrollToEnd() // 每次添加字符后滚动到底部
          } else {
            clearInterval(this.streamingInterval)
            //思考完再输出报告
            this.startStreaming(this.paragraph, 2)
          }
        }, 50) // 调整间隔时间控制速度
      } else {
        this.streamingInterval = setInterval(() => {
          if (currentIndex < chars.length) {
            this.displayReport += chars[currentIndex]
            currentIndex++
          } else {
            this.thinking = false
            clearInterval(this.streamingInterval)
          }
        }, 50) // 调整间隔时间控制速度
      }

      const scrollToEnd = () => {
        this.$nextTick(() => {
          const container = this.$refs.aiThinking?.$el || this.$refs.aiThinking
          if (container) {
            const scrollContainer =
              container.querySelector('.thinkContent') || container
            if (scrollContainer) {
              scrollContainer.scrollTop = scrollContainer.scrollHeight
            }
          }
        })
      }
    },
    generateReport() {
      this.showAi = true
      this.thinking = true
      // this.getAiReport()
      this.getAiReport2()
    },
    handleThinking() {
      this.showThinking = !this.showThinking
    },
    async toImage() {
      let that = this
      this.getImging = true
      this.$nextTick(() => {
        // 第一个参数是需要生成截图的元素,第二个是自己需要配置的参数,宽高等
        return html2canvas(document.getElementById('pdfContent'), {
          backgroundColor: '#f9fafb', // 背景颜色
          // dpi: 192, // 将分辨率提高到特定的dpi,默认值为96
          // scale: 2, // 用于渲染的比例尺。默认为浏览器设备像素比率。默认值是1，手机端设置成2
          useCORS: true, // 是否尝试使用CORS从服务器加载图像
        }).then((canvas) => {
          that.posterUrl = canvas.toDataURL('image/png')
          // console.log(that.posterUrl, 'url')
          var img = new Image()
          img.src = that.posterUrl
          img.onload = () => {
            // 获取dom高度、宽度
            img.width = img.width / 2
            img.height = img.height / 2
            // console.log(img.width, '------ img.width')
            // console.log(img.height, '------img.height')
            img.style.transform = 'scale(0.5)'
            var pdf = new jsPDF('p', 'mm', [
              img.width * 0.505,
              img.height * 0.545,
            ])
            pdf.addImage(
              that.posterUrl,
              'jpeg',
              0,
              0,
              img.width * 0.505,
              img.height * 0.545
            )
            pdf.save('简报' + '.pdf') //h5在这就可以保存pd
            that.getImging = false
          }
        })
      })
    },
    textAddDot(list) {
      list.forEach((item, i) => {
        list[i] = list[i].replaceAll(
          'dot_blue',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #0098fa;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
        list[i] = list[i].replaceAll(
          'dot_green',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #0CD9B5;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
        list[i] = list[i].replaceAll(
          'dot_yellow',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #FDCC00;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
        list[i] = list[i].replaceAll(
          'dot_red',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #F27629;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
      })
      this.$forceUpdate()
    },
  },
}
</script>

<style lang="scss" scoped>
.indicator-card {
  margin: 0 25rpx;

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;

    .dot {
      width: 24rpx;
      height: 14rpx;
      margin-right: 16rpx;
    }

    .card-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    .exportBtn {
      padding: 8rpx 20rpx;
      background-color: #fff;
      border-radius: 8rpx;
      font-size: 28rpx;
    }
  }
  .card {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 20rpx;
    box-sizing: border-box;
    margin-bottom: 30rpx;
  }
  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 20rpx;
    border-radius: 10rpx;

    .icon-container {
      border-radius: 30rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20rpx;

      .iconfont {
        width: 60rpx;
        height: 60rpx;
      }
    }

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
    }
  }
}
.listCon {
  padding: 20rpx 40rpx;
  box-sizing: border-box;
  font-size: 36rpx;
  .icon {
    width: 15rpx;
    height: 15rpx;
    border-radius: 50%;
    margin-right: 14rpx;
  }
  .listItem {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30rpx;
    color: #666666;
    line-height: 44rpx;
    text-align: left;
    margin: 14rpx 0;
  }
}
.textList {
  padding: 20rpx 0;
  box-sizing: border-box;
  .textItem {
    margin: 20rpx 0;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30rpx;
    color: #222222;
    line-height: 43rpx;
    text-align: justify;
    .divider {
      width: 100%;
      height: 1px;
      background-image: linear-gradient(
        to right,
        #cfd2dc 0%,
        #cfd2dc 50%,
        transparent 50%
      );
      background-size: 8px 1px;
      background-repeat: repeat-x;
      margin-bottom: 20rpx;
    }
  }
}
::v-deep .parseClass {
  white-space: pre-wrap;
}
.aiBtn {
  width: 100%;
  height: 90rpx;
}
.aiThinking {
  .thinkHeader {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30rpx;
    color: #960cff;
    line-height: 36rpx;
    text-align: left;
    margin-bottom: 10rpx;
    .star {
      width: 24rpx;
      height: 24rpx;
      margin-right: 8rpx;
    }
    .icon {
      width: 22rpx;
      height: 14rpx;
      transition: 0.3s;
    }
    .icon_rotated {
      transform: rotate(180deg);
      transition: 0.3s;
    }
  }
  .thinkContent {
    padding: 0 20rpx;
    box-sizing: border-box;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 24rpx;
    text-align: justify;
    color: #96a1ad;
    line-height: 36rpx;
    height: auto;
    max-height: 200rpx;
    overflow-y: scroll;
  }
  .thinkContent_all {
    height: unset;
    overflow-y: unset;
    max-height: unset;
  }
}
.aiReport {
  margin-top: 10rpx;
}
</style>
