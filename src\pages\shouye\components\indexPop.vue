<template>
  <u-popup :show="show" @close="close" mode="center" closeable round="12">
    <view class="detailPop">
      <view class="title">{{ detailInfo.indicator_name }}</view>
      <view class="itemList">
        <view class="item">
          <view class="label">当前值：</view>
          <view class="value">
            {{ detailInfo.value || '-' }}
          </view>
        </view>
        <view class="item">
          <view class="label">责任部门：</view>
          <view class="value">
            {{ detailInfo.responsible_department }}
          </view>
        </view>
        <view class="item">
          <view class="label">更新时间：</view>
          <view class="value">
            {{ detailInfo.updateTime || '-' }}
          </view>
        </view>
        <view class="item">
          <view class="label">更新频率：</view>
          <view class="value">
            {{ detailInfo.update_frequency }}
          </view>
        </view>
      </view>
      <!-- 折线图 -->
      <view style="width: 100%; height: 330rpx">
        <LEchart ref="detailLineChart" style="width: 100%; height: 100%" />
      </view>
    </view>
  </u-popup>
</template>

<script>
import * as echarts from '@/pages/shouye/components/lime-echart/static/echarts.min.js'
import LEchart from '@/pages/shouye/components/lime-echart/components/l-echart/l-echart.vue'
export default {
  components: { LEchart },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    detailInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      demo: [
        // { indicator_name: '一般公共预算收入', dept: '市财政局', time: '月' },
        // { indicator_name: '地区生产总值', dept: '市统计局', time: '季' },
        // { indicator_name: '规上工业增加值', dept: '市统计局', time: '月' },
        // {
        //   indicator_name: '工业战略性新兴产业增加值占规模以上工业增加值比重',
        //   dept: '市统计局',
        //   time: '月',
        // },
        // { indicator_name: '固定资产投资', dept: '市统计局', time: '月' },
        // {
        //   indicator_name: '民间投资占固定资产投资比重',
        //   dept: '市统计局',
        //   time: '月',
        // },
        // { indicator_name: '在测市场主体数', dept: '市市场监管局', time: '日' },
        // {
        //   indicator_name: '当日新增市场主体数',
        //   dept: '市市场监管局',
        //   time: '日',
        // },
        // {
        //   indicator_name: '当日注销市场主体数',
        //   dept: '市市场监管局',
        //   time: '日',
        // },
        // { indicator_name: '全社会用电量', dept: '市电业局', time: '日' },
        // { indicator_name: '金华市实时人口', dept: '每日互动', time: '实时' },
        // { indicator_name: '当前累计出生人数', dept: '市公安局', time: '日' },
        // { indicator_name: '当前累计死亡人数', dept: '每日互动', time: '日' },
      ],
      detailLineData: {
        // xAxis: [
        //   '2023-11',
        //   '2023-12',
        //   '2024-01',
        //   '2024-02',
        //   '2024-03',
        //   '2024-04',
        // ],
        // values: [50, 60, 40, 80, 50, 70],
      },
      detailLineOptions: {
        grid: {
          top: 30,
          right: 0,
          bottom: 10,
          left: 0,
          containLabel: true,
        },
        xAxis: {
          type: 'category',

          data: '',
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
            fontSize: 10,
          },
        },
        yAxis: {
          type: 'value',
          // name: '单位：度',
          min: 0,
          max: 100,
          interval: 20,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
            },
          },
          axisLabel: {
            color: '#999',
            // formatter: '{value}%',
            fontSize: 10,
          },
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}%',
        },
        series: [
          {
            data: '',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 2,
              color: '#2979ff',
            },
            itemStyle: {
              color: '#2979ff',
            },
          },
        ],
      },
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.detailLineChart.init(echarts, (e) => {
        this.detailLineOptions.xAxis.data = this.detailLineData.xAxis
        this.detailLineOptions.series[0].data = this.detailLineData.values
        e.setOption(this.detailLineOptions)
      })
    })
  },
  methods: {
    close() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="scss" scoped>
.detailPop {
  padding: 30rpx;
  box-sizing: border-box;
  .title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 34rpx;
    color: #1d2129;
    line-height: 48rpx;
    margin-bottom: 24rpx;
  }
  .itemList {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    .item {
      width: 50%;
      margin-bottom: 24rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 30rpx;
      color: #4e5969;
      line-height: 42rpx;
      display: flex;
      align-items: center;
    }
  }
}
</style>
