<template>
  <view
    :style="[{backgroundColor: showBc ? '#fff' : 'transparent'},{margin:showBc ? '20rpx' :''},{borderRadius:showBc?'20rpx':'0'},{padding:showBc?'10rpx 10rpx':'0'}]">
    <view v-if="showTitle" :class="['nav-bar',showBc?'':'borderBtom']">
      <view class="flex" style="align-items: center;">
        <view class="title_before" :style="{'backgroundColor': partConfig.bgcolor ? partConfig.bgcolor:'#23a4ff'}" v-if="partConfig.titleBefore!==false">
        </view>
        <text class="nav-bar-title" :style="{'padding-left': partConfig.titleBefore!==false ? '10rpx':'0'}" >{{partConfig.title}}</text>

      </view>
      <view v-if="showCollsape && partConfig.menus.length>=limit+1">
        <text v-if="!isShowAll" style="font-size: 20rpx; color: #767676;" class="iconfont icon-jia"
          @click="showAll">展开</text>
        <text v-if="isShowAll" style="font-size: 20rpx; color: #767676;" class="iconfont icon-jia"
          @click="closeAll">收起</text>
      </view>
      <view v-else-if="showMore">
        <!-- <view v-else-if="showMore && partConfig.menus.length>=limit+1"> -->
        <text style="font-size: 20rpx; color: #0285e4;" class="iconfont icon-jia" @click="onClickMore">更多</text>
      </view>
    </view>
    <view class="menu-panel menu-panel-grid" >
      <slot></slot>

    </view>

  </view>
</template>

<script>
  export default {
    name: "menu-part",
    props: {
      partConfig: {
        type: Object
      },

      showTitle: {
        type: Boolean,
        default: true
      },
      showMore: {
        type: Boolean,
        default: true
      },
      showCollsape: {
        type: Boolean,
        default: false
      },

      showBc: {
        type: Boolean,
        default: true
      },

      height: {
        type: String,
        default: '180rpx'
      },
      showSlot: {
        type: Boolean,
        default: true
      },
    },
    watch: {

    },
    data() {
      return {
        menu_array: [],
        isShowAll: false,
        edit: false
      };
    },
    created() {

    },
    mounted() {},
    methods: {

      onClickMore() {

        this.$emit("onClickMore", this.partConfig)
      },

    }
  }
</script>

<style lang="scss" scoped>
  .nav-bar {
    margin: 10rpx 10rpx;
    font-size: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
.borderBtom{
  border-bottom: 1rpx solid #a89f9f7a;
  padding-bottom: 15rpx;
}
  .nav-bar-title {
    font-size: 33rpx;
    /* font-weight: bold; */
    color: #3c3b3c;

    /* border-left: 5px solid #23a4ff; */

  }

  .menu-panel {}

  .menu-panel-grid {

    display: flex;
    justify-content: flex-start;
    align-items: center;
    /* flex-direction: column; */
    /* padding: 0 20rpx; */
    flex-wrap: wrap;
    align-items: flex-start;

  }

  .scroll-pannel-grid {
    padding-top: 30rpx;

    .menu-item {
      // width: 237rpx !important;
      height: 126rpx !important;

    }

    .small_title {
      margin: 0;
      color: #222222 !important;
      font-size: 32rpx;
    }
  }

  .menu-gtid-item {
    flex: 1;
  }

  .menu-item {
    position: relative;
    box-sizing: border-box;
    /* background-color: #00c4ff; */
    box-shadow: 5rpx 5rpx 10rpx 0rpx rgba(96, 96, 96, 0.29);
    border-radius: 7rpx;
    /* height: 180rpx; */
    margin: 0 10rpx 20rpx;
    padding: 32rpx 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    /* background-image: url("@/static/img/shouye/jjtj-bc.png"); */
    background-size: 100% 100%;
    background-position: 0 0;
  }

  .menu-item-left {
    /* margin-right: 10rpx; */
    /* box-shadow: unset; */
  }

  .limit3-left-item,
  .limit3-right-item {
    box-shadow: unset !important;
  }

  .menu-item-right {
    /* margin-left: 10rpx; */
    /* box-shadow: unset; */
  }

  .menu-item-low {
    height: 205rpx;
    color: #fff;
  }

  .menu-item-high {
    height: 430rpx;
    color: #fff;
  }

  .menu-item-low-text,
  .menu-item-high-text {
    font-weight: 700;
    font-family: MicrosoftYaHei;
  }

  .portrait-img {
    position: absolute;
    top: -20rpx;
    left: -20rpx;
    width: 130%;
    height: 100%;
    font-size: 30rpx;
    text-align: center;
    margin-top: 20rpx;
    display: block;
    z-index: -999;
  }

  .containe_list {
    display: flex;
    justify-content: space-evenly;
    width: 25%;
  }

  .title_before {
    width: 10rpx;
    height: 25rpx;
    background-color: #23a4ff;
    border-radius: 5rpx
  }

  .small_title {
    margin-left: 50rpx;
    position: relative;
    top: -7rpx;
  }

  button {
    color: #2682fa;
    font-size: 30rpx;
    font-weight: 400;
    width: 120rpx;
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 40rpx;
    margin: 0;
  }

  .flex {
    display: flex;
  }

  .scroll-list {
    @include flex(column);

    &__goods-item {
      margin-right: 20px;
      width: 300rpx;

      &__image {
        width: 60px;
        height: 60px;
        border-radius: 4px;
      }

      &__text {
        color: #f56c6c;
        text-align: center;
        font-size: 12px;
        margin-top: 5px;
      }
    }

    &__show-more {
      background-color: #fff0f0;
      border-radius: 3px;
      padding: 3px 6px;
      @include flex(column);
      align-items: center;

      &__text {
        font-size: 12px;
        width: 12px;
        color: #f56c6c;
        line-height: 16px;
      }
    }
  }

  .column1-item {
    display: flex;
    border-bottom: 2rpx solid #F5F6F8;
    height: 132rpx;
    padding: 0 30rpx;
    button{
      width: 180rpx;
      height: 64rpx;
      background: #E4EDFF;
      border-radius: 32px 32px 32px 32px;
      opacity: 1;
      color: #000;
      border: unset;
      position: unset;

    }
    uni-button:after{
      width: 0;
      height: 0;
    }
    .column1-item-img {
      width: 80rpx;
      height: 80rpx;
      margin-right: 20rpx;


    }
    .small_title1 {
      font-size: 32rpx;

    }

    .subtitle {
      line-height: 20rpx;
      font-size: 25rpx;
      color: #999999;
    }
  }
</style>
