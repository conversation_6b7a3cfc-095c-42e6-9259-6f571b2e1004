<template>
  <view>
    <!-- Tab内容区域 -->
    <view class="tab-content">
      <view v-if="currentTab === 0">
        <jj />
      </view>
      <view v-if="currentTab === 1">
        <ms />
      </view>
      <view v-if="currentTab === 2">
        <hj />
      </view>
      <view v-if="currentTab === 3">
        <pa />
      </view>
    </view>
  </view>
</template>

<script>
import jj from './sskb/jj.vue'
import ms from './sskb/ms.vue'
import hj from './sskb/hj.vue'
import pa from './sskb/pa.vue'

export default {
  components: {
    jj,
    ms,
    hj,
    pa,
  },
  props: {
    currentTab: {
      type: Number,
      default: 0,
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
