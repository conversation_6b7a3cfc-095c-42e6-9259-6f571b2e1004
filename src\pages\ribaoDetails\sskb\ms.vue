<template>
  <view class="wrap">
    <view class="economy-container">
      <!-- 头部图标和标题 -->
      <view class="header">
        <view class="icon-container">
          <image src="@/static/img/home/<USER>" class="iconfont" />
        </view>
        <text class="title">民生</text>
      </view>

      <view class="indicator-card">
        <view class="indicator-content">
          <view
            class="indicator-item blue-bg"
            v-for="(item, i) in wellbeingData.slice(0, 1)"
            :key="i + 'e'"
            @click="showDetail(item)"
          >
            <view class="item-title">{{ item.title }}</view>
            <view class="item-value" style="margin-bottom: 30rpx">
              {{ item.value || '-' }}
              <text class="unit" v-if="item.unit">{{ item.unit }}</text>
            </view>
            <view class="flex-b">
              <view class="update-time">
                更新时间: {{ item.updateTime || '-' }}
              </view>
              <view class="update-time">数源单位: {{ item.sydw || '-' }}</view>
            </view>
          </view>

          <cardRow
            :list="wellbeingData.slice(1, 3)"
            :bg="'purple'"
            @showDetail="showDetail"
          ></cardRow>
        </view>
      </view>

      <!-- 图1：居民人均可支配收入 -->
      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">居民人均可支配收入</text>
        </view>

        <view class="indicator-content">
          <view class="chart-card">
            <view class="chart-header">
              <view class="chart-subtitle">
                <text class="update-time">数源单位：市统计局</text>
                <text class="update-time">更新日期: -</text>
              </view>
            </view>
            <view style="width: 100%; height: 400rpx; margin-bottom: 10px">
              <LEchart ref="incomeChart" />
            </view>
          </view>
        </view>
      </view>

      <!-- 图2：城乡居民人均可支配收入占比 -->
      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">城乡居民人均可支配收入占比</text>
        </view>

        <view class="indicator-content">
          <view class="chart-card">
            <view class="flex-c chart-header">
              <view class="tabList">
                <view
                  class="tab"
                  v-for="(x, i) in tabList"
                  :key="i"
                  :class="tabIndex == i ? 'tab_active' : ''"
                  @click="changeTab(i)"
                >
                  {{ x }}
                </view>
              </view>
            </view>
            <view class="chart-subtitle">
              <text class="update-time">数源单位：市统计局</text>
              <text class="update-time">更新日期: -</text>
            </view>
            <view style="width: 100%; height: 440rpx; margin-bottom: 10px">
              <LEchart ref="incomeTrendChart" />
            </view>
          </view>
        </view>
      </view>

      <!-- 图3：政务服务办件量 -->
      <view class="indicator-card" style="position: relative">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">政务服务办件量</text>
        </view>

        <view class="indicator-content">
          <view class="chart-card">
            <view class="chart-header">
              <view class="chart-subtitle">
                <text class="update-time">数源单位：市政管办</text>
                <text class="update-time">更新日期: 2025-07-07</text>
              </view>
            </view>
            <view style="width: 100%; height: 400rpx; margin-bottom: 10px">
              <LEchart ref="funnelChart" />
            </view>
          </view>
        </view>
        <view class="rateBox">
          <view class="rateLine">
            <view class="line line1"></view>
            <view class="text">
              转化率
              <span class="value">{{ funnelList[0] || '-' }}%</span>
            </view>
          </view>
          <view class="rateLine">
            <view class="line line2"></view>
            <view class="text">
              转化率
              <span class="value">{{ funnelList[1] || '-' }}%</span>
            </view>
          </view>
        </view>
      </view>

      <!-- 图4：实时人流量 -->
      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">实时人流量</text>
        </view>

        <view class="indicator-content">
          <view class="chart-card">
            <view class="chart-header">
              <view class="chart-subtitle">
                <text class="update-time">数源单位：每日互动</text>
                <text class="update-time" style="float: left">
                  更新日期:{{
                    heatMapData.length > 0
                      ? heatMapData[0].days_times
                      : lastDate
                  }}
                </text>
              </view>
            </view>
            <view style="width: 100%; height: 250px; margin-bottom: 10px">
              <LEchart ref="heatMapChart" />
            </view>
          </view>
        </view>
      </view>

      <!-- 图5：重要民生商品价格 -->
      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">重要民生商品价格</text>
        </view>
        <view class="indicator-content">
          <view class="chart-card">
            <view class="chart-header">
              <view class="chart-subtitle" style="margin-bottom: 20rpx">
                <text class="update-time">数源单位：市发改委</text>
                <text class="update-time">
                  更新日期:{{
                    priceData.length > 0 ? priceData[0].stat_date : '-'
                  }}
                </text>
              </view>
            </view>
          </view>
          <view class="price-table" v-if="priceShow">
            <view class="table-header">
              <view class="col product">品种名称</view>
              <view class="col spec">规格等级</view>
              <view class="col unit">计量单位</view>
              <view class="col change">价格变化</view>
            </view>

            <view
              class="table-row"
              v-for="(item, index) in priceData"
              :key="index"
            >
              <!-- :style="{ backgroundColor: item.bg }" -->
              <view class="col product">{{ item.product }}</view>
              <view class="col spec">{{ item.spec }}</view>
              <view class="col unit">{{ item.pUnit }}</view>
              <view class="col change" :class="item.change > 0 ? 'up' : 'down'">
                {{ item.change }}
                <text v-if="item.change > 0">↑</text>
                <text v-else-if="item.change < 0">↓</text>
              </view>
            </view>
            <view
              v-if="!isExpend && priceData.length > 0"
              class="tableBtn flex-c"
              @click="handleExpend(1)"
            >
              <view>展开</view>
              <image
                class="icon"
                src="@/static/img/home/<USER>"
              ></image>
            </view>
            <view
              v-if="isExpend && priceData.length > 0"
              class="tableBtn flex-c"
              @click="handleExpend(0)"
            >
              <view>收起</view>
              <image
                class="icon icon_rotated"
                src="@/static/img/home/<USER>"
              ></image>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 指标弹窗 -->
    <view v-if="show">
      <popup
        :show="show"
        :detailInfo="detailInfo"
        @close="show = false"
      ></popup>
    </view>
  </view>
</template>

<script>
import * as echarts from '@/pages/shouye/components/lime-echart/static/echarts.min.js'
import LEchart from '@/pages/shouye/components/lime-echart/components/l-echart/l-echart.vue'
import popup from '@/pages/shouye/components/indexPop.vue'
import cardRow from '@/pages/shouye/components/cardRow.vue'
import priceDataList from '@/pages/ribaoDetails/sskb/priceData.json'
import { getCsdnInterface } from '@/services/csdnIndexApi/index.js'

export default {
  components: {
    LEchart,
    popup,
    cardRow,
  },
  data() {
    return {
      wellbeingData: [
        {
          title: '金华市实时人口',
          value: '',
          unit: '人',
          growthLabel: '同比',
          growth: '',
          updateTime: '',
          sydw: '每日互动',
        },
        {
          title: '当年累计出生人数',
          value: '',
          unit: '人',
          growthLabel: '同比',
          growth: '',
          updateTime: '2025-04-30',
          sydw: '市公安局',
        },
        {
          title: '当年累计死亡人数',
          value: '',
          unit: '人',
          growthLabel: '同比',
          growth: '',
          updateTime: '2025-04-30',
          sydw: '市公安局',
        },
      ],
      upIcon: require('@/static/img/home/<USER>'),
      downIcon: require('@/static/img/home/<USER>'),
      tabList: ['城镇', '农村'],
      tabIndex: 0,
      // 图1：居民人均可支配收入数据
      incomeData: [
        { name: '婺城区', value: 0 }, //20971
        { name: '开发区', value: 0 }, //22246
        { name: '金东区', value: 0 }, //17548
        { name: '兰溪市', value: 0 }, //14845
        { name: '东阳市', value: 0 }, //21000
        { name: '义乌市', value: 0 }, //31668
        { name: '永康市', value: 0 }, //23006
        { name: '浦江县', value: 0 }, //16062
        { name: '武义县', value: 0 }, //16594
        { name: '磐安县', value: 0 }, //14222
      ],
      // 图1：居民人均可支配收入图表配置
      incomeOptions: {
        color: ['#37A2FF'],
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}',
        },
        grid: {
          top: 30,
          right: 20,
          bottom: 10,
          left: 0,
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#333',
            fontSize: 12,
            interval: 0,
            rotate: 40,
          },
        },
        yAxis: {
          type: 'value',
          name: '单位：元',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
              type: 'dashed',
            },
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
        },
        series: {
          type: 'bar',
          barWidth: '60%',
          label: {
            show: true,
            position: 'top',
          },
          data: [],
        },
      },
      // 图2：城乡居民人均可支配收入占比数据
      incomeTrendData: {
        xAxis: [
          '202411',
          '202412',
          '202501',
          '202502',
          '202503',
          '202504',
          '202505',
        ],
        // 不同城市/区县的数据
        series: [
          // {
          //   name: '婺城区',
          //   data: [30, 25, 20, 18, 30, 20, 15],
          //   color: '#36A2EB',
          // },
          // {
          //   name: '金东区',
          //   data: [18, 25, 35, 20, 25, 22, 28],
          //   color: '#4CD1A7',
          // },
          // {
          //   name: '开发区',
          //   data: [38, 32, 35, 28, 48, 30, 42],
          //   color: '#3F51B5',
          // },
          // {
          //   name: '义乌市',
          //   data: [28, 38, 50, 30, 35, 40, 38],
          //   color: '#FFD54F',
          // },
          // {
          //   name: '东阳市',
          //   data: [35, 45, 58, 32, 42, 45, 50],
          //   color: '#FF7043',
          // },
        ],
      },
      // 图2：城乡居民人均可支配收入占比图表配置
      incomeTrendOptions: {
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            let result = params[0].name + '\n'
            params.forEach((param) => {
              result += param.seriesName + ': ' + param.value + '%\n'
            })
            return result.substring(0, result.length - 1)
          },
        },
        legend: {
          top: 0,
          icon: 'circle',
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 12,
          textStyle: {
            fontSize: 12,
          },
        },
        grid: {
          top: 50,
          right: 20,
          bottom: 0,
          left: 0,
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: [],
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#999',
            fontSize: 12,
          },
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 60,
          interval: 20,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
              type: 'dashed',
            },
          },
          axisLabel: {
            color: '#999',
            formatter: '{value}%',
            fontSize: 12,
          },
        },
        series: [],
      },
      // 图3：政务服务办件量数据
      funnelData: [
        // { value: 789, name: '办件总量' },
        // { value: 253, name: '评价数' },
        // { value: 113, name: '差评数' },
        // { value: 59, name: '差评整改数' },
      ],
      funnelList: ['', ''],
      // 图3：政务服务办件量图表配置
      funnelOptions: {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}',
        },
        color: ['#2196F3', '#4CD1A7', '#3B72AD', '#FFD44A'],
        series: [
          {
            name: '漏斗图',
            type: 'funnel',
            sort: 'none',
            left: '0%',
            top: 30,
            bottom: 10,
            width: '70%',
            // min: 0,
            // max: 800,
            minSize: '50%',
            maxSize: '90%',
            gap: 2,
            label: {
              show: true,
              position: 'inside',
              formatter: function (params) {
                return params.name + ': ' + params.value
              },
              fontSize: 14,
              color: '#fff',
            },
            itemStyle: {
              borderWidth: 0,
            },
            emphasis: {
              label: {
                fontSize: 16,
              },
            },
            data: [],
          },
        ],
      },
      heatMapData: [
        // { name: '婺城区', value: 820000 },
        // { name: '金东区', value: 540000 },
        // { name: '义乌市', value: 960000 },
        // { name: '东阳市', value: 430000 },
        // { name: '兰溪市', value: 350000 },
        // { name: '浦江县', value: 400000 },
        // { name: '永康市', value: 650000 },
        // { name: '磐安县', value: 220000 },
        // { name: '武义县', value: 320000 },
      ],
      // 添加热力图点数据
      heatPoints: [],
      // 图4：实时人流量热力图配置
      heatMapOptions: {
        // tooltip: {
        //   trigger: 'item',
        //   formatter: '{b}: {c}人次',
        // },
        visualMap: {
          min: 0,
          max: 1000,
          left: 'left',
          top: 'bottom',
          // text: ['高', '低'],
          // realtime: false,
          // calculable: false,
          // inRange: {
          //   color: [],
          // },
          splitNumber: 5,
          itemWidth: 14,
          pieces: [
            { min: 2000000, label: '200万人以上' }, // 不指定 max，表示 max 为无限大（Infinity）。
            { min: 1000000, max: 2000000, label: '100-200万人' },
            { min: 600000, max: 1000000, label: '60-100万人' },
            { min: 300000, max: 600000, label: '30-60万人' },
            { min: 0, max: 300000, label: '0-30万人' },
          ],
          inRange: {
            color: ['#CCEAFE', '#99D6FD', '#66C1FC', '#33ADFB', '#0098FA'],
          },
        },
        // geo: {
        //   map: 'jinhua',
        //   label: {
        //     show: true,
        //   },
        //   roam: false,
        //   zoom: 1.2,
        //   // left: '28%',
        //   label: {
        //     show: true,
        //     color: '#333',
        //   },
        //   itemStyle: {
        //     areaColor: '#f0f8ff',
        //     borderColor: '#6fa0e5',
        //     borderWidth: 1,
        //   },
        //   emphasis: {
        //     label: {
        //       color: '#333',
        //     },
        //     itemStyle: {
        //       areaColor: '#ccdfff',
        //     },
        //   },
        // },
        series: [
          // 底图 - 显示区域边界和名称
          {
            name: '金华市',
            type: 'map',
            map: 'jinhua',
            roam: false,
            zoom: 1.2,
            left: '32%',
            selectedMode: false,
            label: {
              show: true,
              color: '#333',
              formatter: function (params) {
                return params.name + '\n' + params.value
              },
            },
            itemStyle: {
              areaColor: '#f0f8ff',
              borderColor: '#0D8EE0',
              borderWidth: 1,
            },
            emphasis: {
              label: {
                color: '#333',
              },
              itemStyle: {
                areaColor: '#ccdfff',
              },
            },
            data: [],
          },
          // 热力图层
          // {
          //   map: 'jinhua',
          //   name: '热力图',
          //   type: 'heatmap',
          //   coordinateSystem: 'geo',
          //   data: [],
          //   pointSize: 8,
          //   blurSize: 12,
          //   minOpacity: 0.2,
          //   maxOpacity: 0.8,
          // },
          //散点图
          // {
          //   type: 'scatter',
          //   coordinateSystem: 'geo',
          //   data: [],
          //   // symbolSize: 8,
          //   symbolSize: function (val) {
          //     return Math.max(val[2] / 10, 8)
          //   },
          //   label: {
          //     show: false,
          //   },
          //   itemStyle: {
          //     color: '#ec4926',
          //     // shadowBlur: 20,
          //     // shadowColor:'#ec9f26',
          //   },
          //   z:1
          // },
        ],
      },
      // 图5：重要民生商品价格数据
      priceData: [],
      priceDataIinitial: [],
      priceShow: true,
      isExpend: false,
      lastDate: '',
      //弹窗
      show: false,
      detailInfo: {},
    }
  },
  onLoad() {},
  mounted() {
    this.lastDate = this.getLastDate()
    this.init()
    this.$nextTick(() => {
      this.initCharts()
    })
  },
  methods: {
    init() {
      let that = this
      //实时人口
      getCsdnInterface('csrk_ssrsldrs', { addressCode: 330700 }).then((res) => {
        if (res.responsecode == 200) {
          this.wellbeingData[0].value = res.data[0].population_count
          this.wellbeingData[0].updateTime = res.data[0].insert_time.slice(
            0,
            10
          )
        }
      })
      //当日累计出生
      getCsdnInterface('ldrb_ljcsrs').then((res) => {
        if (res.responsecode == 200) {
          this.wellbeingData[1].value = res.data[0].tjz
          this.wellbeingData[1].updateTime = res.data[0].gxsj
        }
      })
      //当日累计死亡
      getCsdnInterface('ldrb_ljswrs').then((res) => {
        if (res.responsecode == 200) {
          this.wellbeingData[2].value = res.data[0].tjz
          this.wellbeingData[2].updateTime = res.data[0].gxsj
        }
      })
      //政务服务办件量-漏斗图
      const promises = []
      let url = ['ldrb_zwbjl', 'ldrb_zwbjpjs', 'ldrb_zwbjcps']
      url.forEach((item) => {
        promises.push(getCsdnInterface(item))
      })
      Promise.all(promises)
        .then((res) => {
          this.funnelData = res.map((x, j) => {
            return {
              name:
                j == 0
                  ? '办件总量'
                  : j == 1
                  ? '评价数'
                  : j == 2
                  ? '差评数'
                  : '',
              value: x.data[0].tjz,
              unit: x.data[0].tjdw,
            }
          })

          this.funnelData.forEach((item, i) => {
            if (i !== 0) {
              this.funnelList[i - 1] = parseFloat(
                this.funnelData[i].value / this.funnelData[i - 1].value
              ).toFixed(2)
            }
          })

          // 初始化图3：政务服务办件量漏斗图
          this.$refs.funnelChart.init(echarts, (chart) => {
            this.funnelOptions.series[0].data = this.funnelData
            chart.setOption(this.funnelOptions)
          })
        })
        .catch((error) => {
          console.error('No valid data found:', error)
        })

      //商品价格表
      let msLastDate = ''
      getCsdnInterface('ldrb_jhssplsjgjcbg').then((res) => {
        if (res.responsecode == 200) {
          msLastDate = res.data[res.total - 1].stat_date
          getCsdnInterface('ldrb_jhssplsjgjcbg', {
            stat_date: msLastDate,
          }).then((res) => {
            this.priceDataIinitial = res.data.map((item) => {
              return {
                pUnit: (item.price ? item.price : '-') + item.unit,
                ...item,
              }
            })
            this.$nextTick(() => {
              this.handlePriceData() //整理数据 变化为0的收起
            })
          })
        }
      })

      // 初始化图4：实时人流量热力图
      // 生成热力散点数据
      // this.heatPoints = this.generateHeatPoints()
      getCsdnInterface('csrk_xsqssrk').then((res) => {
        this.heatMapData = res.data.map((item) => {
          return {
            name: item.address_name,
            value: item.population_count,
            days_times: item.days_times,
          }
        })
        //婺城区人口=城脑婺城区+城脑开发区，金东区人口=城脑金义新区
        let index1 = this.heatMapData.findIndex((item) => item.name == '婺城区')
        let index2 = this.heatMapData.findIndex(
          (item) => item.name == '金义新区'
        )
        let index3 = this.heatMapData.findIndex(
          (item) => item.name == '金华开发区'
        )
        this.heatMapData[index1].value =
          parseInt(this.heatMapData[index1].value) +
          parseInt(this.heatMapData[index3].value)
        this.heatMapData.push({
          name: '金东区',
          value: this.heatMapData[index2].value,
          days_times: this.heatMapData[index2].days_times,
        })

        this.$refs.heatMapChart.init(echarts, (chart) => {
          // 注册金华市地图
          echarts.registerMap('jinhua', require('@/static/json/jinhua.json'))

          this.heatMapOptions.series[0].data = this.heatMapData
          // this.heatMapOptions.series[1].type = 'heatmap'
          // this.heatMapOptions.series[1].data = this.heatPoints
          chart.setOption(this.heatMapOptions)
        })
      })
    },
    changeTab(i) {
      this.tabIndex = i
    },
    showDetail(item) {
      this.detailInfo.title = item.title
      this.show = true
    },
    initCharts() {
      // 初始化图1：居民人均可支配收入图表
      this.$refs.incomeChart.init(echarts, (chart) => {
        this.incomeOptions.xAxis.data = this.incomeData.map((item) => item.name)
        this.incomeOptions.series.data = this.incomeData.map(
          (item) => item.value
        )
        chart.setOption(this.incomeOptions)
      })

      // 初始化图2：城乡居民人均可支配收入占比图表
      this.$refs.incomeTrendChart.init(echarts, (chart) => {
        const series = this.incomeTrendData.series.map((item) => {
          return {
            name: item.name,
            type: 'line',
            data: item.data,
            symbol: 'circle',
            symbolSize: 0,
            lineStyle: {
              width: 2,
              color: item.color,
            },
            itemStyle: {
              color: item.color,
            },
          }
        })

        this.incomeTrendOptions.xAxis.data = this.incomeTrendData.xAxis
        this.incomeTrendOptions.legend.data = this.incomeTrendData.series.map(
          (item) => item.name
        )
        this.incomeTrendOptions.series = series
        chart.setOption(this.incomeTrendOptions)
      })
    }, // 生成热力图散点数据
    generateHeatPoints() {
      // 金华市各区县的大致中心坐标(经纬度)
      const centerPoints = {
        婺城区: [119.65, 29.08],
        金东区: [119.7, 29.12],
        义乌市: [120.08, 29.31],
        东阳市: [120.25, 29.28],
        兰溪市: [119.48, 29.21],
        浦江县: [119.9, 29.46],
        永康市: [120.05, 29.0],
        磐安县: [120.45, 29.05],
        武义县: [119.82, 28.9],
      }

      const points = []

      this.heatMapData.forEach((item) => {
        if (centerPoints[item.name]) {
          const center = centerPoints[item.name]
          const pointCount = Math.min(Math.floor(item.value / 100), 10) // 限制最大点数量为10个

          // 为每个区县添加一个集中点
          points.push([center[0], center[1], item.value / 2])

          // 添加周围的散点
          for (let i = 0; i < pointCount; i++) {
            // 在中心点周围随机生成点
            const offsetLng = (Math.random() - 0.5) * 0.1
            const offsetLat = (Math.random() - 0.5) * 0.1
            const value = Math.floor(Math.random() * 100) + 50

            points.push([center[0] + offsetLng, center[1] + offsetLat, value])
          }
        }
      })
      return points
    },
    handleExpend(type) {
      this.isExpend = this.isExpend == 1 ? 0 : 1
      if (type) {
        this.priceData = this.priceDataIinitial
      } else {
        this.handlePriceData()
      }
    },
    handlePriceData() {
      let arr = []
      let subArr = []
      this.priceDataIinitial.forEach((item, i) => {
        if (item.change !== 0) {
          arr.push(item)
        } else {
          subArr.push(item)
        }
      })
      // console.log(arr, 'arr')
      // console.log(subArr, 'subarr')
      this.priceData = arr.sort(this.sortRule)
      this.priceDataIinitial = arr.concat(subArr)
      // this.handleTable() //合并单元格背景色统一
    },
    handleTable() {
      let index = 0 //记录上一个有product的
      let bg1 = '#fff'
      let bg2 = '#EDF7FF'
      this.priceData.forEach((item, i) => {
        if (item.product !== '') {
          index = i
          if (i == 0) {
            item.bg = bg1
          } else {
            item.bg = this.priceData[i - 1].bg == bg1 ? bg2 : bg1
          }
        } else {
          item.bg = this.priceData[index].bg
        }
      })
      this.priceShow = true
      // console.log(this.priceData)
    },
    sortRule(a, b) {
      return Math.abs(b.change) - Math.abs(a.change)
    },
    getLastDate() {
      let lastDate = new Date(new Date().getTime() - 60 * 60 * 24 * 1000)
      let dateStr =
        lastDate.getFullYear() +
        '-' +
        (lastDate.getMonth() + 1) +
        '-' +
        lastDate.getDate()
      return dateStr
    },
  },
}
</script>

<style lang="scss" scoped>
.economy-container {
  box-sizing: border-box;
  border-radius: 18rpx 18rpx 0 0;
  background: url(@/static/img/home/<USER>
  background-size: 100% auto;

  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 20rpx;

    .icon-container {
      background-color: #fff;
      border-radius: 30rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20rpx;

      .iconfont {
        width: 60rpx;
        height: 60rpx;
      }
    }

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .indicator-card {
    background-color: #fff;
    border-radius: 16rpx;
    margin: 0 25rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;

      .dot {
        width: 24rpx;
        height: 14rpx;
        margin-right: 16rpx;
      }

      .card-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .indicator-content {
      .indicator-item {
        padding: 20rpx;
        border-radius: 12rpx;
        margin-bottom: 20rpx;
        text-align: center;

        &.blue-bg {
          background: url(@/static/img/home/<USER>
          background-size: 100% 100%;
        }

        .item-title {
          height: 92rpx;
          display: flex;
          flex-direction: column;
          justify-content: center;
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 700;
          font-size: 40rpx;
          color: #333333;
          line-height: 46rpx;
          text-align: center;
        }

        .item-value {
          font-size: 48rpx;
          font-weight: bold;
          color: #ff6b18;
          margin-bottom: 10rpx;

          .unit {
            font-size: 30rpx;
            font-weight: normal;
            margin-left: 8rpx;
          }
        }

        .item-growth {
          font-size: 32rpx;
          color: #8e98a4;
          margin-bottom: 30rpx;

          .growth-value {
            margin-left: 8rpx;
            &.up {
              color: #cd2020;
            }
            &.down {
              color: #1aa269;
            }
          }
        }

        .update-time {
          font-size: 30rpx;
          color: #586779;

          .time {
            color: #586779;
            font-weight: 500;
            margin-top: 5rpx;
          }
        }
        .update-icon {
          width: 30rpx;
          height: 30rpx;
          margin-right: 10rpx;
        }
      }

      .chart-card {
        background-color: #fff;
        border-radius: 12rpx;
        margin-top: 30rpx;

        .chart-header {
          margin-bottom: 20rpx;

          .chart-title {
            font-size: 36rpx;
            font-weight: 500;
            color: #2c3542;
            margin-bottom: 15rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .chart-subtitle {
          color: #999;
          font-size: 24rpx;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-content: center;
          align-items: center;
        }
        .chart-box {
          width: 100%;
          height: 400rpx;
        }
      }
    }
  }
  .price-table {
    width: 100%;
    border-radius: 8rpx;
    overflow: hidden;

    .table-header {
      display: flex;
      background-color: #e6f3ff;
      font-weight: bold;
      font-size: 28rpx;
      color: #333;

      .col {
        padding: 24rpx 16rpx;
        text-align: center;
        flex: 1;
      }
    }

    .table-row {
      display: flex;
      background-color: #e6f3ff4d;
      font-size: 28rpx;
      color: #333;

      &:nth-child(even) {
        background-color: #fff;
      }

      .col {
        padding: 24rpx 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        text-align: center;
      }
      .up {
        color: #cd2020;
      }
      .down {
        color: #5acd20;
      }
    }
  }
  .tableBtn {
    margin-top: 20rpx;
    font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
    font-weight: 400;
    font-size: 28rpx;
    color: #0098fa;
    width: 100%;
    justify-content: center;
    .icon {
      width: 28rpx;
      height: 28rpx;
      margin-left: 8rpx;
    }
    .icon_rotated {
      transform: rotate(180deg);
    }
  }
}
.tabList {
  display: flex;
  align-items: center;
  .tab {
    border-radius: 32rpx;
    padding: 10rpx 34rpx;
    box-sizing: border-box;
    border: 1rpx solid #aaaaaa;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 26rpx;
    color: #222222;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 30rpx;
    white-space: nowrap;
  }
  .tab_active {
    background-color: #0098fa;
    border-color: #0098fa;
    color: #fff;
  }
}
.rateBox {
  position: absolute;
  right: 40rpx;
  top: 250rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  .rateLine {
    margin-bottom: 70rpx;
    display: flex;
    align-items: center;
    color: #a0a4a4;
    font-size: 28rpx;
    .value {
      color: #333;
      margin-left: 8rpx;
    }

    .line {
      height: 1rpx;
      background-color: #41618027;
    }
    .line1 {
      width: 90rpx;
    }
    .line2 {
      width: 110rpx;
    }
    // .line3 {
    //   width: 130rpx;
    // }
  }
}
</style>
