::v-deep .uni-tabbar {
  padding-bottom: 40rpx !important;
}

html,
body,
page {
  width: 100%;
  min-height: 100%;
  // display: flex;
}

uni-page-body {
  width: 100%;
  height: 100%;
  display: unset;
  border: unset !important;
}
uni-page-wrapper {
  display: block;
  height: 100%;
  position: relative;
}

//第一种
scroll-view ::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
//第二种
::-webkit-scrollbar {
  display: none;
}

.uni-tabbar-border {
  background-color: rgba(0, 0, 0, 0.1) !important;
}
.uni-tabbar__mid {
  width: 140rpx !important;
  height: 140rpx !important;
  // background-image: url(/h5/static/img/tabbar/qr.png) !important;
  border-top: 2rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 53%;
  background-repeat: no-repeat;
  background-position: 20rpx 12rpx;
  background-color: #fff;
  background-size: 76% !important;
}
.flex-c-c {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between-c {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around-c {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-column-c-c {
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: center;
}

.flex-c {
  display: flex;
  align-items: center;
}
.pad20 {
  padding: 0 20rpx;
}
.bc_white {
  background-color: #fff !important;
}
.fs28 {
  font-size: 28rpx !important;
}
.fs30 {
  font-size: 30rpx !important;
}
.fs32 {
  font-size: 32rpx !important;
}
.fs24 {
  font-size: 24rpx !important;
}
.fs26 {
  font-size: 26rpx !important;
}
.fs34 {
  font-size: 34rpx !important;
}
.fs36 {
  font-size: 36rpx !important;
}
.text-nowrap {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.text-nowrap2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; //两行
  -webkit-box-orient: vertical;
}

.max_line1 {
  display: -webkit-box;
  /* autoprefixer: ignore next */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  word-break: break-word;
}

.max_line2 {
  display: -webkit-box;
  /* autoprefixer: ignore next */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-word;
}

.colorYel {
  color: #f49d2c;
}
.mar20 {
  margin: 20rpx 0;
}
.avatar {
  width: 80rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 50%;
  background-color: #007aff;
  color: #fff;
  text-align: center;
}

.avatar image {
  width: 100%;
  height: 100%;
}
.mright20 {
  margin-right: 20rpx;
}

.colorBlue {
  color: rgb(2, 133, 228);
}
.colorGrey0 {
  color: #5c6269;
}
.colorGrey {
  color: #717070;
}
.colorGrey1 {
  color: #999;
}
.fw {
  font-weight: 700;
}
.bgfff {
  background-color: #fff;
}
::v-deep .u-cell__value {
  color: #999999 !important;
}

.textcenter {
  text-align: center;
}
.page-gray {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f9fafb;
  z-index: -1;
}
::v-deep .u-action-sheet__item-wrap {
  max-height: 75vh;
  overflow-y: auto;
}
img {
  max-width: 100%;
  height: auto;
}
.submit-footer {
  position: fixed;
  z-index: 20;
  bottom: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 32rpx 40rpx;
  background: #fff;
  box-shadow: 4px 4px 8px 4px rgba(205, 205, 205, 1);
}
.ws1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.ws2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; //两行
  -webkit-box-orient: vertical;
}
/* 修改返回按钮的图标 */

::v-deep .page-navigation-bar .uni-icon-back {
  // background-image: url(images/back.png);
  background-image: url('https://bwshq.dsjj.jinhua.gov.cn:7443/fileServer/ajhApplet/static/img/back.png') !important;
}
.mar_bottom_20 {
  margin-bottom: 20rpx;
}
.mar_bottom_10 {
  margin-bottom: 10rpx;
}
.mar_right_20 {
  margin-right: 20rpx;
}
.mar_right_10 {
  margin-right: 10rpx;
}
.mar_left_20 {
  margin-left: 20rpx;
}
.mar_left_10 {
  margin-left: 10rpx;
}
.colorRed {
  color: #f14053;
}
.relative {
  position: relative;
}
.flex-b {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
