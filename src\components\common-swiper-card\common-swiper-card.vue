<template>
	<view class="swiper_container">
			<swiper :indicator-dots="true"  circular="true" :autoplay="true" :interval="3000" :duration="2000">
				<swiper-item v-for="(item,index) in swiper_array" :key="index" >
					<view class="swiper-item" @click="goHtml(item)"  :style="{ backgroundImage: `url(${imgUrl(item.img_src)})` }" >

						<!-- <img class="img" :src="require(`@/static/img/swiper/${item.img_src}`)" @click="goHtml(item)" alt=""> -->
					</view >
				</swiper-item>

			</swiper>
	</view>
</template>

<script>
	export default {
		name: "common-swiper-card",
		data() {
			return {
				// pic:require('@/static/img/swiper/gs.png')
			}
		},
		props:{
		swiper_array:{
			type:Array,
			default:()=>[]
		}
		},
		methods: {
			// goHtml(item){
			// 	console.log(item);
			// 	uni.navigateTo({
			// 		url:'https://uniapp.dcloud.net.cn/component/swiper.html'
			// 	})
			// },
			imgUrl (src) {

			          // return require(`@/static/img/${src}`)
			          // return require(src)
			          return src


			},
			goHtml(item){
        if(item.url.length==0) return
        if(!item.link) return
				console.log(item);

				let extra = {
					url:item.url,
					title:item.title,
					type:'iframe'
				}
				uni.navigateTo({
					url:'/pages/my/webView/webView?extra='+encodeURIComponent(JSON.stringify(extra))
				})
			}
		}
	}
</script>

<style scoped>
.img{
	width: 100%;
	/* height: 100%; */

}
.swiper_container{
	margin: 20rpx 0;
  height: 280rpx;
  border-radius: 20rpx;
}
.swiper-item{
	background-position: 0 0;
	background-repeat: no-repeat;
	background-size: 100% 100%;
  height: 280rpx;
  border-radius: 20rpx;

}
uni-swiper{
	height: 280rpx;
  border-radius: 20rpx;
}
::v-deep swiper{
  height: 280rpx;
  border-radius: 20rpx;
}
/* .swiper-item{
	background: url('@/static/img/swiper/gs.png');
} */
/* style="background-image: url(@/static/img/swiper/gs.png);" */
</style>
