<template>
  <view class="shouye-index">
    <view class="titleCon">金华城市大脑</view>
    <view class="main-wrap">
      <view class="banner">
        <image
          style="width: 100%; height: 100%"
          :src="banner1"
          mode="widthFix"
        />
        <view class="btn btn2" @click="changeReportType"></view>
      </view>
      <sjjb></sjjb>
    </view>
    <view class="page-gray"></view>
  </view>
</template>

<script>
import sjjb from './sjjb.vue'

export default {
  components: {
    sjjb,
  },
  data() {
    return {
      banner1: require('@/static/img/home/<USER>'),
    }
  },
  onLoad() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight

    dd.ready(() => {
      dd.getAuthCode({})
        .then((res) => {
          dd.alert({ message: res.code, title: res.code })
          if (res.code) {
            // code.value = res.code
            // loginCommon({
            //   authCode: res.code,
            // }).then((res1) => {
            //   if (res1.code === 200) {
            //     store.setToken(res1.token)
            //     store.fetchUserInfo()
            //     router.push({
            //       path: '/home',
            //     })
            //   }
            // })
          }
        })
        .catch((err) => {
          console.log(err)
        })
    })
  },
  onShow() {},
  onPageScroll(e) {},
  methods: {
    changeReportType() {
      uni.navigateTo({
        url: '/pages/ribaoDetails/index',
      })
    },
  },
  mounted() {},
  onPullDownRefresh() {},
  onReachBottom() {},
}
</script>

<style lang="scss" scoped>
.shouye-index {
  position: relative;
  .titleCon {
    width: 100%;
    padding: 20px 0;
    box-sizing: border-box;
    background-color: #fff;
    text-align: center;
  }
  .main-wrap {
    .banner {
      padding: 30rpx 25rpx 0 25rpx;
      box-sizing: border-box;
      width: 100%;
      position: relative;
      // height: 200rpx;
      image {
        border-radius: 12rpx;
      }
      .btn {
        width: 126rpx;
        height: 36rpx;
        z-index: 1;
        position: absolute;
        top: 50rpx;
        right: 26rpx;
      }
      .btn1 {
        background: url('@/static/img/home/<USER>');
        background-size: 100% 100%;
      }
      .btn2 {
        background: url('@/static/img/home/<USER>');
        background-size: 100% 100%;
      }
    }

    .tab-nav {
      display: flex;
      justify-content: space-around;
      padding: 0 25rpx 20rpx 25rpx;
      background-color: #f9fafb;
      z-index: 10;
      width: 100%;
      box-sizing: border-box;

      &.fixed-tab {
        position: fixed;
        left: 0;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
      }

      .tab-item {
        padding: 20rpx 0;
        position: relative;
        font-size: 36rpx;
        color: #666;

        &.active {
          color: #000;
          font-weight: bold;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60rpx;
            height: 6rpx;
            background-color: #2979ff;
            border-radius: 4rpx;
          }
        }
      }
    }

    // 占位元素，高度与tab导航栏相同
    .tab-placeholder {
      height: 80rpx; // 这个高度应该与tab-nav的实际高度一致
    }

    .tab-content {
      // padding: 20rpx 0rpx;
      min-height: 300rpx;
    }
  }
}
</style>
