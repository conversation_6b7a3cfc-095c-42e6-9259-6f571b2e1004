<template>
  <view>
    <!-- Tab内容区域 -->
    <view
      class="tab-content"
      style="padding-bottom: 40rpx"
      ref="pdfContent"
      id="pdfContent"
    >
      <view class="indicator-card">
        <view class="card-header flex-b">
          <view class="flex-c">
            <image src="@/static/img/home/<USER>" class="dot" />
            <text class="card-title">AI概览</text>
          </view>
          <view class="exportBtn" @click="exportRibao">导出简报</view>
        </view>
        <view class="aiReport">
          <!-- <u-parse
            ref="aiReport"
            :content="thinkingContent"
            class="parseClass"
          ></u-parse> -->
          <p>
            金华市6月经济呈现工业生产与出口双增长态势。增值税开票销售收入周同比8.52%和工业用电量稳步上升（6月19日单日达18.3亿千瓦时）形成联动，印证制造业产能释放。出口方面，中欧班列发送标箱数同比增长12.38%，叠加义乌外商人数增长4.06%，直接推动海铁联运箱量增长12.7%，反映国际市场订单需求旺盛。值得注意的是，义乌机场旅客吞吐量增长8.1%与外商活跃度形成正相关，建议加强国际商贸配套服务以巩固出口优势。
          </p>
          <p>
            消费领域呈现结构性特征，银联线下消费同比激增84.1%与快递业务量增长5.97%形成互补，显示线上线下消费同步复苏。但需警惕机动车上牌量年度累计下降7.94%对耐用品消费的抑制作用，建议适时出台汽车消费刺激政策。房地产市场分化明显，商品房月度成交增长19.47%而二手房年度成交下降21.04%，反映新房政策效应显现但存量市场承压，需防范房企资金链风险。
          </p>
          <p>
            社会运行指标中，PM2.5浓度下降24%与工业用电量增长并存，显示环保技术改造初见成效，建议持续推动绿色生产补贴。市场主体方面，新增数量增长22.9%但注销率同步下降32.2%，显示营商环境改善，可进一步简化注销流程提升市场新陈代谢效率。食品安全价格波动较小，但需关注带鱼、鲫鱼等水产品价格下降对养殖户的影响，建议建立价格预警机制。
          </p>
          <p>
            从2025年5月下旬至7月初金华市用电量走势可见，高温天气与用电需求呈现显著正相关。6月中下旬起，随着气温持续攀升至35-37℃（如6月26日35℃、6月27日37℃），日用电量突破1000万千瓦时关口，6月30日达年度峰值1054.56万千瓦时。此阶段天气以晴热为主，东南风弱，居民制冷需求激增，直接推动用电量月同比增长15.8%。值得注意的是，6月29日受小雨天气影响气温略降（27-34℃），当日用电量回落至992.11万千瓦时，体现出极端高温对用电曲线的决定性作用。7月初持续37℃高温使首日用电量仍维持1036万千瓦时高位，印证夏季空调负荷的核心驱动地位。数据表明，气温每升高1℃约带动日用电量增加30-50万千瓦时，极端高温天气已成为夏季电力供需平衡的关键变量。
          </p>
        </view>
      </view>

      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">每日摘要</text>
        </view>
        <!-- 经济 -->
        <view class="card" v-if="textList1.length > 0">
          <view class="header" :style="{ backgroundColor: '#0098fa1a' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">经济</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList1" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
          <!-- <view style="width: 100%; height: 330rpx">
            <LEchart ref="lineChart" style="width: 100%; height: 100%" />
          </view> -->
        </view>
        <!-- 民生 -->
        <view class="card" v-if="textList2.length > 0">
          <view class="header" :style="{ backgroundColor: '#F276291A' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">民生</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList2" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
        </view>
        <!-- 环境 -->
        <view class="card" v-if="textList3.length > 0">
          <view class="header" :style="{ backgroundColor: '#0CD9B51A' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">环境</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList3" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
        </view>
        <!-- 平安 -->
        <view class="card" v-if="textList4.length > 0">
          <view class="header" :style="{ backgroundColor: '#FDCC001A' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">平安</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList4" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import html2canvas from 'html2canvas'
import { jsPDF } from 'jspdf'
import * as echarts from '@/pages/shouye/components/lime-echart/static/echarts.min.js'
import LEchart from '@/pages/shouye/components/lime-echart/components/l-echart/l-echart.vue'
import { getCsdnInterface } from '@/services/csdnIndexApi/index.js'

export default {
  components: {
    LEchart,
  },
  props: {},
  watch: {},
  data() {
    return {
      list: [
        { name: '经济', color: '#0098FA', text: '表现优秀（指数≥90）' },
        { name: '民生', color: '#0CD9B5', text: '正常、良好（90>指数≥80）' },
        { name: '平安', color: '#FDCC00', text: '小幅度异常（80>指数≥60）' },
        { name: '环境', color: '#F27629', text: '大幅度异常（60>指数≥0）' },
      ],
      textList1: [
        '1.全社会用电量。2025年7月1日用电量1036.89万千瓦，6月30日1054.56万千瓦，6月29日992.11万千瓦，6月26日1041.05万千瓦，显示月末用电需求波动明显。6月19日至25日期间用电量最高达996.82万千瓦，最低为798.92万千瓦，呈现周期性变化。',
        '2.工业用电量变化趋势。7月1日工业用电量1.00亿千瓦时，6月30日30.10亿千瓦时，6月29日29.10亿千瓦时，临近月末工业用电量显著增长。6月19日至26日工业用电量从18.30亿千瓦时逐步攀升至25.90亿千瓦时，反映生产活动加强。',
        '3.在册市场主体数。7月2日市场主体数为2091259家，与7月1日及6月30日持平，6月28日减少至2091245家，6月27日回落至2090602家，整体保持稳定。',
        '4.新增与注销市场主体数。6月28日新增市场主体15家，6月27日新增1657家，6月24日新增1772家，市场活力集中在月末释放。6月28日注销市场主体643家，6月29日注销14家，其他日期注销数量为0，整体注销量较低。',
      ],
      textList2: [
        '1.人口自然变动。截至7月2日，今年累计出生29798人，死亡16459人，人口自然增长率为正。',
      ],
      textList3: [
        '1.空气质量（PM2.5与PM10）。7月1日PM2.5浓度：兰溪市42.01微克/立方米，开发区35.96微克/立方米，义乌市29.03微克/立方米，区域间差异显著。PM10浓度最高为兰溪市52.83微克/立方米，最低为武义县23.51微克/立方米。',
      ],
      textList4: [
        '1.信访数据。6月30日信访批次7961起、人数7963人，6月29日批次4467起、人数4467人，月末信访量激增。6月21日至24日信访批次稳定在3920-4691起，人数同步波动，社会矛盾需重点关注。',
        '2.公共安全指标。6月27日治安警情6865起，6月26日7279起，6月25日7511起，治安压力逐日上升。生产安全事故数与社会安全指标未更新，需结合历史数据持续监测。',
      ],
      //大模型
      thinkingContent: '', //接口返回的answer
      //导出
      posterUrl:
        'https://csdn.dsjj.jinhua.gov.cn:8101/taskReply/fileManage/%E9%87%91%E5%8D%8E%E5%B8%82%E5%9F%8E%E5%B8%82%E5%A4%A7%E8%84%91%E6%97%A5%E6%8A%A5_0703.docx',
    }
  },
  filters: {
    addDot(value) {
      return
    },
  },
  mounted() {
    // this.getAiReport2()
  },
  methods: {
    async getAiReport2() {
      //每日更新的数据库,模拟流式
      getCsdnInterface('ldrb_zysc').then((res) => {
        if (res.responsecode == 200) {
          this.paragraph = res.data[0].content
          // console.log(this.paragraph);
          // this.paragraph = this.paragraph.replaceAll('<think', '<p')
          this.paragraph = this.paragraph.replaceAll('############', '')
          const arr1 = this.paragraph.split('<think>')
          const arr11 = arr1[1].split('</think>') //获取周报数据比对arr11[1]
          const arr12 = arr1[2].split('</think>') //获取用电量和气温变化关系分析arr12[1]
          this.thinkingContent =
            '<p>' + arr11[0] + '</p>\n<p>' + arr12[0] + '</p>'
          this.paragraph = '<p>' + arr11[1] + '</p>\n<p>' + arr12[1] + '</p>'
        }
      })
    },
    exportRibao() {
      var a = document.createElement('a')
      const url = this.posterUrl
      a.href = url
      a.click()
      window.URL.revokeObjectURL(url)
    },
    textAddDot(list) {
      list.forEach((item, i) => {
        list[i] = list[i].replaceAll(
          'dot_blue',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #0098fa;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
        list[i] = list[i].replaceAll(
          'dot_green',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #0CD9B5;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
        list[i] = list[i].replaceAll(
          'dot_yellow',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #FDCC00;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
        list[i] = list[i].replaceAll(
          'dot_red',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #F27629;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
      })
      this.$forceUpdate()
    },
  },
}
</script>

<style lang="scss" scoped>
.indicator-card {
  margin: 0 25rpx;

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;

    .dot {
      width: 24rpx;
      height: 14rpx;
      margin-right: 16rpx;
    }

    .card-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    .exportBtn {
      padding: 8rpx 20rpx;
      background-color: #fff;
      border-radius: 8rpx;
      font-size: 28rpx;
    }
  }
  .card {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 20rpx;
    box-sizing: border-box;
    margin-bottom: 30rpx;
  }
  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 20rpx;
    border-radius: 10rpx;

    .icon-container {
      border-radius: 30rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20rpx;

      .iconfont {
        width: 60rpx;
        height: 60rpx;
      }
    }

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
    }
  }
}
.listCon {
  padding: 20rpx 40rpx;
  box-sizing: border-box;
  font-size: 36rpx;
  .icon {
    width: 15rpx;
    height: 15rpx;
    border-radius: 50%;
    margin-right: 14rpx;
  }
  .listItem {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30rpx;
    color: #666666;
    line-height: 44rpx;
    text-align: left;
    margin: 14rpx 0;
  }
}
.textList {
  padding: 20rpx 0;
  box-sizing: border-box;
  .textItem {
    margin: 20rpx 0;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30rpx;
    color: #222222;
    line-height: 43rpx;
    text-align: justify;
    .divider {
      width: 100%;
      height: 1px;
      background-image: linear-gradient(
        to right,
        #cfd2dc 0%,
        #cfd2dc 50%,
        transparent 50%
      );
      background-size: 8px 1px;
      background-repeat: repeat-x;
      margin-bottom: 20rpx;
    }
  }
}
::v-deep .parseClass {
  white-space: pre-wrap;
}
.aiBtn {
  width: 100%;
  height: 90rpx;
}
.aiThinking {
  .thinkHeader {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30rpx;
    color: #960cff;
    line-height: 36rpx;
    text-align: left;
    margin-bottom: 10rpx;
    .star {
      width: 24rpx;
      height: 24rpx;
      margin-right: 8rpx;
    }
    .icon {
      width: 22rpx;
      height: 14rpx;
      transition: 0.3s;
    }
    .icon_rotated {
      transform: rotate(180deg);
      transition: 0.3s;
    }
  }
  .thinkContent {
    padding: 0 20rpx;
    box-sizing: border-box;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 24rpx;
    text-align: justify;
    color: #96a1ad;
    line-height: 36rpx;
    height: auto;
    max-height: 200rpx;
    overflow-y: scroll;
  }
  .thinkContent_all {
    height: unset;
    overflow-y: unset;
    max-height: unset;
  }
}
.aiReport {
  margin-top: 10rpx;
}
</style>
