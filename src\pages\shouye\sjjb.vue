<template>
  <view>
    <!-- Tab内容区域 -->
    <view
      class="tab-content"
      style="padding-bottom: 40rpx"
      ref="pdfContent"
      id="pdfContent"
    >
      <view class="indicator-card">
        <view class="card-header flex-b">
          <view class="flex-c">
            <image src="@/static/img/home/<USER>" class="dot" />
            <text class="card-title">AI概览</text>
          </view>
          <view class="exportBtn" @click="exportRibao">导出简报</view>
        </view>
        <view class="aiReport">
          <!-- <u-parse
            ref="aiReport"
            :content="thinkingContent"
            class="parseClass"
          ></u-parse> -->
          <p class="text_i">
            从多维度数据交叉分析可见，金华市经济运行呈现结构性分化特征。增值税开票销售收入（周同比+12.53%）与工业用电量（月累计+13%）同步增长，表明制造业产能释放加速，建议加强重点产业链供需监测，防范原材料价格波动风险。出口领域指标显著向好，中欧班列发送量（周同比+34%）与海铁联运箱量（周同比+30.7%）形成叠加效应，但跨境电商通道的邮件互换局包裹量却大幅下降（周同比-48.5%），反映出贸易渠道结构正在发生深刻变革，需推动海外仓建设与多式联运体系优化。消费市场呈现冰火两重天，银联线下消费（周同比+137%）与机动车上牌量（周同比+9.94%）显示大宗消费复苏，但酒店入住人数（本年累计-9.29%）持续疲软，建议针对商旅市场制定精准刺激政策。
          </p>
          <p class="text_i">
            民生领域需关注价格结构性波动，猪肉价格（周同比-13.9%）与带鱼价格下行减轻居民生活压力，但芹菜（周涨幅+24.8%）、油菜（+10.7%）等蔬菜价格上涨明显，暴露出本地蔬菜供应链薄弱环节，应扩大季节性蔬菜储备规模。房地产市场呈现新房与二手房市场分化，新房成交面积（周同比+16.95%）回暖而二手房（周同比-42.03%）交易遇冷，需警惕新房集中交付可能引发的库存压力，建议完善二手房交易税费优惠政策。
          </p>
          <p class="text_i">
            安全环保指标改善显著，生产安全事故数（月累计-33.33%）、交通安全亡人数（月累计-22.22%）与PM2.5浓度（周同比-34.1%）同步下降，证明综合治理措施见效，但突发命案（周增1起）提示基层治理存在薄弱点，应加强重点区域网格化管理。市场主体活跃度保持韧性，新设主体（周同比+52%）与注销主体（周同比-14.5%）呈现良性更替，但需注意单日新增市场主体波动较大（7月5日仅11家），建议优化企业全生命周期服务体系。
          </p>
          <p class="text_i">
            从2025年5月下旬至7月初，金华市全社会用电量呈现波动上升趋势，天气因素与用电需求呈现显著关联。6月中下旬起，随着气温逐步攀升至35-37℃区间，日用电量多次突破900万千瓦时，7月3日达到峰值1113.18万千瓦时。高温天气直接刺激了制冷设备的使用，尤其是6月30日至7月3日连续四天气温高达37℃期间，用电量保持高位运行，月同比增速提升至6.9%。此外，行业用电在6月下旬出现明显拐点，月同比增长由负转正，可能与高温环境下企业增加通风降温设备运行有关。值得注意的是，尽管7月6日后出现降雨天气，但日间温度仍维持在25-29℃，基础制冷需求持续存在，使得用电量维持在1100万千瓦时以上。整体而言，夏季高温是推高用电需求的核心因素，温度峰值与用电量峰值呈现高度同步性，而间歇性降雨虽短暂缓解高温压力，但未能显著降低整体用电负荷。
          </p>
        </view>
      </view>

      <view class="indicator-card">
        <view class="card-header">
          <image src="@/static/img/home/<USER>" class="dot" />
          <text class="card-title">每日摘要</text>
        </view>
        <!-- 经济 -->
        <view class="card" v-if="textList1.length > 0">
          <view class="header" :style="{ backgroundColor: '#0098fa1a' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">经济</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList1" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
          <!-- <view style="width: 100%; height: 330rpx">
            <LEchart ref="lineChart" style="width: 100%; height: 100%" />
          </view> -->
        </view>
        <!-- 民生 -->
        <view class="card" v-if="textList2.length > 0">
          <view class="header" :style="{ backgroundColor: '#F276291A' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">民生</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList2" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
        </view>
        <!-- 环境 -->
        <view class="card" v-if="textList3.length > 0">
          <view class="header" :style="{ backgroundColor: '#0CD9B51A' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">环境</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList3" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
        </view>
        <!-- 平安 -->
        <view class="card" v-if="textList4.length > 0">
          <view class="header" :style="{ backgroundColor: '#FDCC001A' }">
            <view class="icon-container">
              <image src="@/static/img/home/<USER>" class="iconfont" />
            </view>
            <text class="title">平安</text>
          </view>
          <view class="textList">
            <view class="textItem" v-for="(item, i) in textList4" :key="i">
              <view class="divider" v-if="i !== 0"></view>
              <u-parse :content="item"></u-parse>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import html2canvas from 'html2canvas'
import { jsPDF } from 'jspdf'
import * as echarts from '@/pages/shouye/components/lime-echart/static/echarts.min.js'
import LEchart from '@/pages/shouye/components/lime-echart/components/l-echart/l-echart.vue'
import { getCsdnInterface } from '@/services/csdnIndexApi/index.js'

export default {
  components: {
    LEchart,
  },
  props: {},
  watch: {},
  data() {
    return {
      list: [
        { name: '经济', color: '#0098FA', text: '表现优秀（指数≥90）' },
        { name: '民生', color: '#0CD9B5', text: '正常、良好（90>指数≥80）' },
        { name: '平安', color: '#FDCC00', text: '小幅度异常（80>指数≥60）' },
        { name: '环境', color: '#F27629', text: '大幅度异常（60>指数≥0）' },
      ],
      textList1: [
        '1.能源消费领域。全社会用电量月累计达13.5亿千瓦时，同比增长8.2%，显示经济活跃度持续提升。工业用电量变化趋势稳定在3.2亿千瓦时，年同比增幅5.3%，其中婺城区单日工业用电量达1113.18万千瓦。义乌市以258.3万实时人口支撑的制造业基地，单日用电量突破1096.94万千瓦时。',
        '2.市场活力指标。新增市场主体数呈现波动特征，7月5日单日增量仅11家，但近三日（7月1-3日）日均新增超1500家。义乌市场日均外商稳定在4052人，带动快递业务量周均3.6亿件，同比增长10%。银联线下消费周交易额204亿元，同比激增137%，消费市场表现强劲。',
        '3.交通物流网络。高速出入口车流量呈现明显区域差异：义乌市单日总流量15.6万辆（入口7.73万/出口7.87万），永康市4.45万辆（入口2.23万/出口2.22万），磐安县仅5509辆（入口2914/出口2595）。中欧班列周发运67列，标箱5304个，同比分别增长34%和28.93%，海铁联运9152标箱创近三月新高。',
        '4.房地产市场。商品房成交面积周度13.4万㎡，同比上升16.95%，月度累计81.8万㎡实现23.44%增长。二手房市场相对平淡，单周5.2万㎡成交量同比下降42.03%，但月度25.9万㎡成交量降幅收窄至10.43%。',
      ],
      textList2: [
        '1.人口自然变动政务服务效能。政务办件总量单日21069件，差评率0.48%（101件/总评价1494件）。办件评价率7.09%较上月提升1.2个百分点，群众满意度指数稳定在93分区间。出生登记、市场主体注册等高频事项平均办理时长压缩至1.5工作日。',
        '2.交通出行服务。金义东轨道交通日发送旅客14.66万人次（周均102.6万），铁路客运量9.92万人次/日。机动车新上牌量日均960辆，新能源车占比提升至38%。全市酒店入住率68.4%，较上周提升3个百分点。',
      ],
      textList3: [
        '1.空气质量指标。PM2.5浓度呈现北高南低特征：兰溪市41.44μg/m³，开发区35.35μg/m³，武义县13.98μg/m³最优。PM10浓度婺城区57.93μg/m³居首，磐安县33.66μg/m³最低。开发区大气压强1013.15hPa，温度18.96℃属舒适区间。',
        '2.县域环境对比。东阳市综合环境指数最佳：PM2.5值21.04μg/m³，温度18.03℃，湿度61.1%。义乌市虽PM2.5达28.29μg/m³，但依托37.98μg/m³ PM10值和0.55m/s风速，污染物扩散条件较好。磐安县持续保持空气质量优势项，PM2.5/PM10双指标均低于市均水平40%。',
      ],
      textList4: [
        '1.信访态势分析。单日信访量下降至4461批次/4491人次，较月初7794批次峰值下降42.7%。群体性事件发生率0.17‰，主要集中劳资纠纷（34%）、物业矛盾（28%）领域。重复访率压降成效显著，从月初15.3%降至9.8%。',
        '2.安全生产管理。本周未新增生产安全事故，月度累计2起同比下降33%。交通安全亡人数21人，超速驾驶占比事故原因62%。消防警情周均35起，电气火灾占比升至47%，开展专项排查整治行动3次。',
        '3.治安防控体系。治安警情日处置158起，同比下降5.54%，盗窃类案件占比41%仍居首位。"雪亮工程"新增监控点位127个，重点区域覆盖率提升至98.7%。命案侦破率保持100%，反诈预警劝阻成功率创新高达79.3%。',
      ],
      //大模型
      thinkingContent: '', //接口返回的answer
      //导出
      posterUrl:
        'https://csdn.dsjj.jinhua.gov.cn:8101/taskReply/fileManage/%E5%9F%8E%E5%B8%82%E5%A4%A7%E8%84%91%E6%97%A5%E6%8A%A5_0707.docx',
    }
  },
  filters: {
    addDot(value) {
      return
    },
  },
  mounted() {
    // this.getAiReport2()
  },
  methods: {
    async getAiReport2() {
      //每日更新的数据库,模拟流式
      getCsdnInterface('ldrb_zysc').then((res) => {
        if (res.responsecode == 200) {
          this.paragraph = res.data[0].content
          // console.log(this.paragraph);
          // this.paragraph = this.paragraph.replaceAll('<think', '<p')
          this.paragraph = this.paragraph.replaceAll('############', '')
          const arr1 = this.paragraph.split('<think>')
          const arr11 = arr1[1].split('</think>') //获取周报数据比对arr11[1]
          const arr12 = arr1[2].split('</think>') //获取用电量和气温变化关系分析arr12[1]
          this.thinkingContent =
            '<p>' + arr11[0] + '</p>\n<p>' + arr12[0] + '</p>'
          this.paragraph = '<p>' + arr11[1] + '</p>\n<p>' + arr12[1] + '</p>'
        }
      })
    },
    exportRibao() {
      var a = document.createElement('a')
      const url = this.posterUrl
      a.href = url
      a.click()
      window.URL.revokeObjectURL(url)
    },
    textAddDot(list) {
      list.forEach((item, i) => {
        list[i] = list[i].replaceAll(
          'dot_blue',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #0098fa;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
        list[i] = list[i].replaceAll(
          'dot_green',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #0CD9B5;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
        list[i] = list[i].replaceAll(
          'dot_yellow',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #FDCC00;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
        list[i] = list[i].replaceAll(
          'dot_red',
          '<view style="width: 15rpx;height: 15rpx;border-radius: 50%;background-color: #F27629;margin:0rpx 10rpx 2rpx 10rpx;display: inline-block;"></view>'
        )
      })
      this.$forceUpdate()
    },
  },
}
</script>

<style lang="scss" scoped>
.indicator-card {
  margin: 0 25rpx;

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;

    .dot {
      width: 24rpx;
      height: 14rpx;
      margin-right: 16rpx;
    }

    .card-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    .exportBtn {
      padding: 8rpx 20rpx;
      background-color: #fff;
      border-radius: 8rpx;
      font-size: 28rpx;
    }
  }
  .card {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 20rpx;
    box-sizing: border-box;
    margin-bottom: 30rpx;
  }
  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 20rpx;
    border-radius: 10rpx;

    .icon-container {
      border-radius: 30rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20rpx;

      .iconfont {
        width: 60rpx;
        height: 60rpx;
      }
    }

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
    }
  }
}
.listCon {
  padding: 20rpx 40rpx;
  box-sizing: border-box;
  font-size: 36rpx;
  .icon {
    width: 15rpx;
    height: 15rpx;
    border-radius: 50%;
    margin-right: 14rpx;
  }
  .listItem {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30rpx;
    color: #666666;
    line-height: 44rpx;
    text-align: left;
    margin: 14rpx 0;
  }
}
.textList {
  padding: 20rpx 0;
  box-sizing: border-box;
  .textItem {
    margin: 20rpx 0;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30rpx;
    color: #222222;
    line-height: 43rpx;
    text-align: justify;
    .divider {
      width: 100%;
      height: 1px;
      background-image: linear-gradient(
        to right,
        #cfd2dc 0%,
        #cfd2dc 50%,
        transparent 50%
      );
      background-size: 8px 1px;
      background-repeat: repeat-x;
      margin-bottom: 20rpx;
    }
  }
}
::v-deep .parseClass {
  white-space: pre-wrap;
}
.aiBtn {
  width: 100%;
  height: 90rpx;
}
.aiThinking {
  .thinkHeader {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 30rpx;
    color: #960cff;
    line-height: 36rpx;
    text-align: left;
    margin-bottom: 10rpx;
    .star {
      width: 24rpx;
      height: 24rpx;
      margin-right: 8rpx;
    }
    .icon {
      width: 22rpx;
      height: 14rpx;
      transition: 0.3s;
    }
    .icon_rotated {
      transform: rotate(180deg);
      transition: 0.3s;
    }
  }
  .thinkContent {
    padding: 0 20rpx;
    box-sizing: border-box;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 24rpx;
    text-align: justify;
    color: #96a1ad;
    line-height: 36rpx;
    height: auto;
    max-height: 200rpx;
    overflow-y: scroll;
  }
  .thinkContent_all {
    height: unset;
    overflow-y: unset;
    max-height: unset;
  }
}
.aiReport {
  margin-top: 10rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
  font-size: 16px;
  .text_i {
    text-indent: 24px;
    margin-bottom: 12px;
  }
}
</style>
