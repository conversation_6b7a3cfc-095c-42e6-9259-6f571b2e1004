<template>
  <view class="banner">

    <image  :animation="current==index?showpic:hidepic"  :class="index==0?'zIndex':''" v-for="(item,index) in swiperArray" :src="item.swiper" mode="" @click="picClick(current)">
    </image>

  </view>
</template>

<script>
  export default {
    name: "swiper-aa",
    props: {
      swiperArray: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        current: 0,
        hidepic: null,
        showpic: null,
        onece: true
      }
    },
    mounted() {
      this.current = -1
      // setTimeout(()=>{
        this.play()
      // },2000)


    },
    methods:{
      picClick(index){
        if(index==-1){
          this.$emit('picClick',0)
        }else{
          this.$emit('picClick',index)
        }
      },
      play(){
        setInterval(() => {
          // if (this.onece) {
          //   this.onece = false
          //   return
          // }

          var animation = uni.createAnimation({}) //创建一个动画实例
          //淡出
          animation.opacity(0).step({
            // delay: 1000,
            duration: 2000
          })
          this.hidepic = animation.export()

          //淡入
          animation.opacity(1).step({
            // delay:1000,
            duration: 3000
          }) //描述动画

          this.showpic = animation.export()
          if (this.current == this.swiperArray.length - 1) {
            this.current = 0
          } else {
            // if (!this.onece) {
              this.current++
            // }
          }
          //输出动画



        }, 5000)
      }
    },
    onShow() {

    }
  }
</script>

<style lang="scss" scoped>
  .banner {
    width: 100%;
    // #ifdef MP-WEIXIN
    height: 200px;
    // #endif
    // #ifdef H5
    height: 150px;
    // #endif
    position: relative;

    image {
      width: 100%;
      height: 100%;
      position: absolute;
    }
    .zIndex{
      z-index: 99999999999999;
    }
  }
</style>
