<template>
  <view class="ct">
    <u-scroll-list indicatorColor="#ebebeb" v-if="scroll || type=='scroll'">
      <view v-for="(item, index) in dataList" :key="index" class="item">
        <image class="img_right" :src="item.right_icon" mode=""></image>
        <view class="top">
          <view class="flex-c">
            <view class="" style="margin-right: 15rpx;line-height: 60rpx;">
              {{item.name}}
            </view>
            <view class="flex-c">
              <view class="tip" v-for="(obj,i) in item.tip" :key="i">
                {{obj}}
              </view>
            </view>
          </view>
          <view class="colorGrey fs28">
            {{item.content}}
          </view>
        </view>
        <view class="bottom flex-between-c">
          <view class="">
            <view style="color: red;">
              <text>¥</text> <text class="fw price">{{item.price}}</text><text class="colorGrey fs26">万</text>
            </view>
          </view>
          <view class="btn" @click="onClickBtn(item)">
            获取额度
          </view>
        </view>
      </view>
    </u-scroll-list>
    <swiper class="swiper" circular :indicator-dots="true" :autoplay="true" indicatorColor="#ebebeb"
      v-else-if="type=='swiper'">


      <swiper-item v-for="(item, index) in dataList" :key="index">
        <view class="item">
          <image class="img_right" :src="item.right_icon" mode=""></image>
          <view class="top">
            <view class="flex-c">
              <view class="" style="margin-right: 15rpx;line-height: 60rpx;">
                {{item.name}}
              </view>
              <view class="flex-c">
                <view class="tip" v-for="(obj,i) in item.tip" :key="i">
                  {{obj}}
                </view>
              </view>
            </view>
            <view class="colorGrey fs28">
              {{item.content}}
            </view>
          </view>
          <view class="bottom flex-between-c">
            <view class="">
              <view style="color: red;">
                <text>¥</text> <text class="fw price">{{item.price}}</text><text class="colorGrey fs26">万</text>
              </view>
            </view>
            <view class="btn" @click="onClickBtn(item)">
              获取额度
            </view>
          </view>
        </view>

      </swiper-item>


    </swiper>
    <view class="noscroll" v-else>
      <view v-for="(item, index) in dataList" :key="index" class="item" style="width: 100%
      ; margin-bottom: 20rpx;">
        <image class="img_right" :src="item.right_icon" mode=""></image>
        <view class="top">
          <view class="flex-c">
            <view class="" style="margin-right: 15rpx;line-height: 60rpx;">
              {{item.name}}
            </view>
            <view class="flex-c">
              <view class="tip" v-for="(obj,i) in item.tip" :key="i">
                {{obj}}
              </view>
            </view>
          </view>
          <view class="colorGrey fs28">
            {{item.content}}
          </view>
        </view>
        <view class="bottom flex-between-c">
          <view class="">
            <view style="color: red;">
              <text>¥</text> <text class="fw price">{{item.price}}</text><text class="colorGrey fs26">万</text>
            </view>
          </view>
          <view class="btn" @click="onClickBtn(item)">
            去借钱
          </view>
        </view>
      </view>

    </view>
  </view>
</template>

<script>
  export default {
    name: "scroll-production",
    props: {
      dataList: {
        type: Array,
        default: () => []
      },
      type: {
        type: String,
        default: 'scroll'
      },
      tabShow: {
        type: Boolean,
        default: false
      },
      indicator: {
        type: Boolean,
        default: false
      },
      scroll: {
        type: Boolean,
        default: true
      },

    },
    computed: {

    },

    data() {
      return {

      };
    },
    methods: {
      onClickBtn(item) {
        this.$emit('onClickItemBtn', item)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .ct {


    .item {
      position: relative;
      width: 554rpx;
      flex: none;

      margin-right: 20rpx;
      background-image: linear-gradient(to right, #f0f7fe 0%, #f4f8fe 40%, #f9fbfb 70%, #fff 100%);


      .top {
        padding: 20rpx;
        border-bottom: 2rpx solid #DFE4ED;

        .tip {
          color: red;
          background: #fdeaec;
          font-size: 26rpx;
          margin-right: 10rpx;
          padding: 0 10rpx;
        }
      }

      .img_right {
        position: absolute;
        right: 10rpx;
        top: 10rpx;
        width: 82rpx;
        height: 82rpx;
      }

      .bottom {
        padding: 15rpx 30rpx;

        .btn {
          color: #428FFC;
          border: 2rpx solid #428FFC;
          border-radius: 30rpx;
          padding: 5rpx 25rpx;
          font-size: 28rpx
        }

        .price {
          display: inline-block;
          margin: 0 10rpx;
          font-size: 45rpx;
        }
      }
    }

    .noscroll {
      .top {
        .tip {
          color: #333 !important;
          background-color: unset;
          font-size: 26rpx;
          margin-right: 10rpx;
          padding: 0 10rpx;
          border: 2rpx solid #ddd4d4;
          border-radius: 10rpx;
        }
      }

      .btn {
        padding: 5rpx 50rpx !important;
      }

    }
  }
  .swiper{
  width: 554rpx;
  }
  swiper-item{
    overflow-x: visible !important;
  }
</style>
