import { http } from '@/utils/http'

// 社区点位录入
export const addCommunityPoi = (data) => {
  const params = {
    url: `/screen/communityPoi/add`,
    method: 'POST',
    data,
  }
  return http(params)
}

// 社区点位编辑
export const editCommunityPoi = (data) => {
  const params = {
    url: `/screen/communityPoi/edit`,
    method: 'POST',
    data,
  }
  return http(params)
}

// 当前账号下手动添加的社区点位列表
export const getCommunityPoiList = (data) => {
  const params = {
    url: `/screen/communityPoi/list`,
    method: 'POST',
    data,
  }
  return http(params)
}

// 社区点位详情
export const getCommunityPoiDetail = (id) => {
  const params = {
    url: `/screen/communityPoi/${id}`,
    method: 'GET',
  }
  return http(params)
}

// 社区点位删除
export const delCommunityPoi = (id) => {
  const params = {
    url: `/screen/communityPoi/${id}`,
    method: 'DELETE',
  }
  return http(params)
}
