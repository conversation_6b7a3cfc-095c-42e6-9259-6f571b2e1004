console.log(process.env.UNI_PLATFORM, 'process.env.UNI_PLATFORM')
if (process.env.UNI_PLATFORM === 'h5') {
  // 由于这种方式的打包，会导致编译生成微信小程序（只验证了微信小程序）无法正常使用，所以必须分开
  let filePath = './static/js/'
  let Timestamp = new Date().getTime()

  const cloudscraper = require('cloudscraper');
  // 启动时先去“刷”一次，拿到 cookie
  let cfCookie = '';
  cloudscraper.get('https://jkpt.yw.gov.cn/ESBWeb/servlets/12345fenlei@1.0?createtime=2023-03-19', {
    resolveWithFullResponse: true
  })
    .then(resp => {
      // headers['set-cookie'] 可能包含多个，挑出 cf_clearance
      const setC = resp.headers['set-cookie'] || [];
      const item = setC.find(s => s.startsWith('cf_clearance='));
      cfCookie = item && item.split(';')[0];
      console.log('拿到 cf_clearance:', cfCookie);
    })
    .catch(err => {
      console.error('cloudscraper 取 cookie 失败', err);
    });

  module.exports = {
    transpileDependencies: ['uview-ui'],
    devServer: {
      proxy: {
        '/adm-api': {
          secure: false,
          // target: 'https://bwshq.dsjj.jinhua.gov.cn:9443',
          target: 'https://csdn.dsjj.jinhua.gov.cn:8300',
          changeOrigin: true,
          pathRewrite: {
            '^/adm-api': '',
          },
        },
        '/csdnMap': {
          changeOrigin: true,
          secure: false,
          target: 'https://csdn.dsjj.jinhua.gov.cn:9601',
          pathRewrite: {
            '^/csdnMap': "https://csdn.dsjj.jinhua.gov.cn:9601"
          },
          onProxyRes(proxyRes, req, res) {
            const realUrl = req.url || ''; // 真实请求网址
            console.log(realUrl); // 在终端显示
            proxyRes.headers['A-Real-Url'] = realUrl; // 添加响应标头(A-Real-Url为自定义命名)，在浏览器中显示
          }
        },
        '/ywApi': {
          changeOrigin: true,
          secure: false,
          target: 'https://jkpt.yw.gov.cn/ESBWeb/servlets',
          pathRewrite: {
            '^/ywApi': ""
          },
          onProxyRes(proxyReq) {
            if (cfCookie) {
              proxyReq.setHeader('Cookie', cfCookie);
              proxyReq.setHeader('Referer', 'https://jkpt.yw.gov.cn/ESBWeb/servlets/');
            }
          }
        }
      },
    },
    // ... webpack 相关配置
    filenameHashing: false,
    configureWebpack: {
      // webpack 配置 解决js缓存的问题，目前只适配H5端打包
      output: {
        // 输出重构  打包编译后的 文件目录 文件名称 【模块名称.时间戳】
        filename: `${filePath}[name]-${Timestamp}.js`,
        chunkFilename: `${filePath}[name]-${Timestamp}.js`,
      },
    },
  }
} else {
  // 其他打包需要的相关配置
  module.exports = {
    transpileDependencies: ['uview-ui'],
    devServer: {
      proxy: {
        '/adm-api': {
          secure: false,
          // target: 'http://192.168.110.174:8500/adm-api',
          // target: 'https://www.aijinhua.cn:8443/adm-api', //正式环境
          target: 'http://172.16.10.97:8500/adm-api',
          changeOrigin: true,
          pathRewrite: {
            '^/adm-api': '',
          },
        },
      },
    },
  }
}
